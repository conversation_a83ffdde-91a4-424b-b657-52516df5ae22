{"permissions": {"allow": ["<PERSON><PERSON>(cd github-blog-generator)", "Bash(mkdir -p backend frontend static templates docs)", "Bash(mkdir -p frontend static/css static/js templates)", "Bash(find /Users/<USER>/gemini cli/github-blog-generator -type f -name \"*.py\" -o -name \"*.html\" -o -name \"*.css\" -o -name \"*.js\" -o -name \"*.md\" -o -name \"*.txt\")", "Bash(chmod +x /Users/<USER>/gemini cli/github-blog-generator/run.py)", "Bash(tree /Users/<USER>/gemini cli/github-blog-generator -I '__pycache__|*.pyc|.git')", "Bash(ls -la /Users/<USER>/gemini cli/github-blog-generator)"], "deny": []}}