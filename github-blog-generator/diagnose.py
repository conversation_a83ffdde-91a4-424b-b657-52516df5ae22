#!/usr/bin/env python3
"""
诊断脚本 - 检查为什么无法访问localhost:5000
"""

import os
import sys
import socket
import subprocess
import time

def check_port_availability():
    """检查端口5000是否可用"""
    print("🔍 检查端口5000状态...")
    
    try:
        # 检查端口是否被占用
        result = subprocess.run(['lsof', '-i', ':5000'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip():
            print("⚠️ 端口5000已被占用:")
            print(result.stdout)
            return False
        else:
            print("✅ 端口5000可用")
            return True
            
    except Exception as e:
        print(f"❌ 检查端口时出错: {e}")
        return False

def check_dependencies():
    """检查依赖是否安装"""
    print("\n📦 检查Python依赖...")
    
    required_packages = ['flask', 'requests', 'matplotlib', 'seaborn', 'pandas']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 请安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_flask_import():
    """测试Flask应用导入"""
    print("\n🐍 测试Flask应用导入...")
    
    try:
        # 切换到项目目录
        project_dir = os.path.dirname(__file__)
        os.chdir(project_dir)
        
        # 添加backend到路径
        sys.path.insert(0, 'backend')
        
        # 尝试导入Flask应用
        from backend.app import app
        print("✅ Flask应用导入成功")
        
        # 检查路由
        print("📍 注册的路由:")
        for rule in app.url_map.iter_rules():
            print(f"  {rule.rule} -> {rule.endpoint}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flask应用导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_socket_connection():
    """测试socket连接"""
    print("\n🌐 测试socket连接...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("✅ 可以连接到localhost:5000")
            return True
        else:
            print("❌ 无法连接到localhost:5000")
            return False
            
    except Exception as e:
        print(f"❌ Socket连接测试失败: {e}")
        return False

def start_test_server():
    """启动测试服务器"""
    print("\n🚀 尝试启动测试服务器...")
    
    try:
        # 切换到项目目录
        project_dir = os.path.dirname(__file__)
        os.chdir(project_dir)
        
        # 添加backend到路径
        sys.path.insert(0, 'backend')
        
        # 导入并启动Flask应用
        from backend.app import app
        
        print("📍 启动地址: http://localhost:5000")
        print("💡 请在浏览器中访问上述地址")
        print("💡 按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 启动服务器
        app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 GitHub博客生成器 - 连接诊断")
    print("=" * 50)
    
    # 执行诊断检查
    checks = [
        ("端口检查", check_port_availability),
        ("依赖检查", check_dependencies),
        ("Flask导入检查", test_flask_import),
        ("Socket连接检查", test_socket_connection)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}执行失败: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有检查通过，尝试启动服务器...")
        start_test_server()
    else:
        print("❌ 发现问题，请根据上述信息进行修复")
        print("\n💡 常见解决方案:")
        print("1. 安装依赖: pip install -r requirements.txt")
        print("2. 检查端口占用: lsof -i :5000")
        print("3. 手动启动: python3 backend/app.py")

if __name__ == "__main__":
    main()
