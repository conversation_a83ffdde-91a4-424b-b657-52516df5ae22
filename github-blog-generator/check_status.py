#!/usr/bin/env python3
"""
检查项目状态
"""

import os
import sys
import requests
import subprocess

def check_files():
    """检查必要文件是否存在"""
    print("📁 检查项目文件...")
    
    required_files = [
        'backend/app.py',
        'backend/github_api.py', 
        'backend/content_generator.py',
        'frontend/index.html',
        'frontend/script.js',
        'frontend/style.css',
        'templates/blog.html',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"  ❌ 缺失文件: {missing_files}")
        return False
    
    print("  ✅ 所有必要文件都存在")
    return True

def check_python_syntax():
    """检查Python文件语法"""
    print("\n🐍 检查Python语法...")
    
    python_files = [
        'backend/app.py',
        'backend/github_api.py',
        'backend/content_generator.py'
    ]
    
    for file_path in python_files:
        try:
            result = subprocess.run([
                sys.executable, '-m', 'py_compile', file_path
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"  ✅ {file_path} 语法正确")
            else:
                print(f"  ❌ {file_path} 语法错误: {result.stderr}")
                return False
        except Exception as e:
            print(f"  ❌ 检查 {file_path} 时出错: {e}")
            return False
    
    return True

def check_imports():
    """检查关键模块导入"""
    print("\n📦 检查模块导入...")
    
    try:
        sys.path.insert(0, 'backend')
        
        # 测试导入
        from flask import Flask
        print("  ✅ Flask 导入成功")
        
        import requests
        print("  ✅ requests 导入成功")
        
        from github_api import GitHubAPI
        print("  ✅ GitHubAPI 导入成功")
        
        from content_generator import ContentGenerator
        print("  ✅ ContentGenerator 导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False

def check_server():
    """检查服务器是否运行"""
    print("\n🌐 检查服务器状态...")
    
    try:
        response = requests.get('http://localhost:5000', timeout=3)
        if response.status_code == 200:
            print("  ✅ 服务器正在运行 (http://localhost:5000)")
            return True
        else:
            print(f"  ⚠️ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("  ❌ 服务器未运行")
        return False
    except Exception as e:
        print(f"  ❌ 检查服务器时出错: {e}")
        return False

def main():
    """主函数"""
    print("🔍 GitHub博客生成器 - 状态检查")
    print("=" * 50)
    
    # 切换到项目目录
    project_dir = os.path.dirname(__file__)
    if project_dir:
        os.chdir(project_dir)
    
    # 执行检查
    checks = [
        ("文件检查", check_files),
        ("语法检查", check_python_syntax), 
        ("导入检查", check_imports),
        ("服务器检查", check_server)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"  ❌ {check_name}执行失败: {e}")
            results.append((check_name, False))
    
    # 显示总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查通过！项目状态正常")
        print("💡 可以访问: http://localhost:5000")
    else:
        print("⚠️ 发现问题，请根据上述信息进行修复")
        print("💡 启动建议:")
        print("  1. 如果服务器未运行: python3 start_server.py")
        print("  2. 如果有语法错误: 检查并修复Python文件")
        print("  3. 如果缺少依赖: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
