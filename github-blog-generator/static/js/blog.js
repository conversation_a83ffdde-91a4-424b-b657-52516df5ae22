// Blog template JavaScript utilities

class BlogUtils {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollToTop();
        this.setupSmoothScrolling();
        this.setupCodeCopy();
        this.setupImageModal();
        this.setupPrintFunction();
        this.setupThemeToggle();
    }

    // 返回顶部功能
    setupScrollToTop() {
        // 创建返回顶部按钮
        const scrollTopBtn = document.createElement('button');
        scrollTopBtn.className = 'scroll-top-btn';
        scrollTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        scrollTopBtn.setAttribute('aria-label', '返回顶部');
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .scroll-top-btn {
                position: fixed;
                bottom: 2rem;
                right: 2rem;
                width: 50px;
                height: 50px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 50%;
                cursor: pointer;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                z-index: 1000;
                font-size: 1.2rem;
            }
            
            .scroll-top-btn.visible {
                opacity: 1;
                visibility: visible;
            }
            
            .scroll-top-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            }
            
            @media (max-width: 768px) {
                .scroll-top-btn {
                    bottom: 1rem;
                    right: 1rem;
                    width: 45px;
                    height: 45px;
                }
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(scrollTopBtn);
        
        // 监听滚动事件
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollTopBtn.classList.add('visible');
            } else {
                scrollTopBtn.classList.remove('visible');
            }
        });
        
        // 点击事件
        scrollTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 平滑滚动到锚点
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // 代码块复制功能
    setupCodeCopy() {
        document.querySelectorAll('.code-block').forEach(codeBlock => {
            const copyBtn = document.createElement('button');
            copyBtn.className = 'code-copy-btn';
            copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
            copyBtn.setAttribute('aria-label', '复制代码');
            
            // 添加复制按钮样式
            if (!document.querySelector('#code-copy-styles')) {
                const style = document.createElement('style');
                style.id = 'code-copy-styles';
                style.textContent = `
                    .code-block {
                        position: relative;
                    }
                    
                    .code-copy-btn {
                        position: absolute;
                        top: 1rem;
                        right: 1rem;
                        background: rgba(255, 255, 255, 0.1);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        color: #a0aec0;
                        padding: 0.5rem;
                        border-radius: 0.375rem;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        font-size: 0.875rem;
                    }
                    
                    .code-copy-btn:hover {
                        background: rgba(255, 255, 255, 0.2);
                        color: white;
                    }
                    
                    .code-copy-btn.copied {
                        background: #10b981;
                        color: white;
                    }
                `;
                document.head.appendChild(style);
            }
            
            codeBlock.appendChild(copyBtn);
            
            copyBtn.addEventListener('click', async () => {
                const code = codeBlock.querySelector('pre').textContent;
                
                try {
                    await navigator.clipboard.writeText(code);
                    copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                    copyBtn.classList.add('copied');
                    
                    setTimeout(() => {
                        copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
                        copyBtn.classList.remove('copied');
                    }, 2000);
                } catch (err) {
                    console.error('复制失败:', err);
                }
            });
        });
    }

    // 图片模态框
    setupImageModal() {
        document.querySelectorAll('img').forEach(img => {
            img.style.cursor = 'pointer';
            img.addEventListener('click', () => {
                this.openImageModal(img.src, img.alt);
            });
        });
    }

    openImageModal(src, alt) {
        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="image-modal-content">
                <button class="image-modal-close">&times;</button>
                <img src="${src}" alt="${alt}" class="modal-image">
                <p class="modal-caption">${alt}</p>
            </div>
        `;
        
        // 添加模态框样式
        if (!document.querySelector('#image-modal-styles')) {
            const style = document.createElement('style');
            style.id = 'image-modal-styles';
            style.textContent = `
                .image-modal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.9);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 2000;
                    animation: fadeIn 0.3s ease;
                }
                
                .image-modal-content {
                    position: relative;
                    max-width: 90%;
                    max-height: 90%;
                    text-align: center;
                }
                
                .modal-image {
                    max-width: 100%;
                    max-height: 80vh;
                    object-fit: contain;
                    border-radius: 0.5rem;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                }
                
                .image-modal-close {
                    position: absolute;
                    top: -40px;
                    right: 0;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 2rem;
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .modal-caption {
                    color: white;
                    margin-top: 1rem;
                    font-size: 0.9rem;
                }
                
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(modal);
        
        // 关闭事件
        modal.addEventListener('click', (e) => {
            if (e.target === modal || e.target.classList.contains('image-modal-close')) {
                modal.remove();
            }
        });
        
        // ESC 键关闭
        document.addEventListener('keydown', function closeOnEsc(e) {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', closeOnEsc);
            }
        });
    }

    // 打印功能
    setupPrintFunction() {
        const printBtn = document.createElement('button');
        printBtn.className = 'print-btn';
        printBtn.innerHTML = '<i class="fas fa-print"></i> 打印博客';
        printBtn.setAttribute('aria-label', '打印博客');
        
        // 添加到页面合适位置
        const header = document.querySelector('.blog-header');
        if (header) {
            header.appendChild(printBtn);
        }
        
        // 添加打印按钮样式
        if (!document.querySelector('#print-btn-styles')) {
            const style = document.createElement('style');
            style.id = 'print-btn-styles';
            style.textContent = `
                .print-btn {
                    position: absolute;
                    top: 1rem;
                    right: 1rem;
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    padding: 0.75rem 1.5rem;
                    border-radius: 2rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-size: 0.9rem;
                    backdrop-filter: blur(10px);
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }
                
                .print-btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: translateY(-1px);
                }
                
                @media print {
                    .print-btn, .scroll-top-btn {
                        display: none !important;
                    }
                }
                
                @media (max-width: 768px) {
                    .print-btn {
                        position: static;
                        margin-top: 1rem;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        printBtn.addEventListener('click', () => {
            window.print();
        });
    }

    // 主题切换
    setupThemeToggle() {
        const themeBtn = document.createElement('button');
        themeBtn.className = 'theme-toggle-btn';
        themeBtn.innerHTML = '<i class="fas fa-moon"></i>';
        themeBtn.setAttribute('aria-label', '切换主题');
        
        // 添加到页面
        const header = document.querySelector('.blog-header');
        if (header) {
            header.appendChild(themeBtn);
        }
        
        // 添加主题切换样式
        if (!document.querySelector('#theme-toggle-styles')) {
            const style = document.createElement('style');
            style.id = 'theme-toggle-styles';
            style.textContent = `
                .theme-toggle-btn {
                    position: absolute;
                    top: 1rem;
                    left: 1rem;
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    padding: 0.75rem;
                    border-radius: 50%;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-size: 1rem;
                    backdrop-filter: blur(10px);
                    width: 45px;
                    height: 45px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .theme-toggle-btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: scale(1.05);
                }
                
                /* 暗色主题 */
                body.dark-theme {
                    background-color: #1a1a1a;
                    color: #e2e8f0;
                }
                
                body.dark-theme .blog-content {
                    background: #2d3748;
                    color: #e2e8f0;
                }
                
                body.dark-theme .section-title {
                    color: #f7fafc;
                }
                
                body.dark-theme .tech-item {
                    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
                    color: #e2e8f0;
                }
                
                body.dark-theme .feature-list li {
                    background: #4a5568;
                    color: #e2e8f0;
                }
                
                body.dark-theme .blog-footer {
                    background: #2d3748;
                    color: #e2e8f0;
                }
                
                @media (max-width: 768px) {
                    .theme-toggle-btn {
                        position: static;
                        margin-bottom: 1rem;
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        // 检查本地存储的主题设置
        const savedTheme = localStorage.getItem('blog-theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeBtn.innerHTML = '<i class="fas fa-sun"></i>';
        }
        
        themeBtn.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            const isDark = document.body.classList.contains('dark-theme');
            
            themeBtn.innerHTML = isDark ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
            localStorage.setItem('blog-theme', isDark ? 'dark' : 'light');
        });
    }

    // 生成目录
    generateTableOfContents() {
        const toc = document.createElement('div');
        toc.className = 'table-of-contents';
        toc.innerHTML = '<h3>目录</h3><ul id="toc-list"></ul>';
        
        const tocList = toc.querySelector('#toc-list');
        const headings = document.querySelectorAll('h2, h3, h4');
        
        headings.forEach((heading, index) => {
            const id = `heading-${index}`;
            heading.id = id;
            
            const li = document.createElement('li');
            li.className = `toc-item toc-${heading.tagName.toLowerCase()}`;
            li.innerHTML = `<a href="#${id}">${heading.textContent}</a>`;
            
            tocList.appendChild(li);
        });
        
        // 添加目录样式
        if (!document.querySelector('#toc-styles')) {
            const style = document.createElement('style');
            style.id = 'toc-styles';
            style.textContent = `
                .table-of-contents {
                    background: #f8f9ff;
                    border: 1px solid #e2e8f0;
                    border-radius: 1rem;
                    padding: 2rem;
                    margin: 2rem 0;
                    position: sticky;
                    top: 2rem;
                }
                
                .table-of-contents h3 {
                    margin-bottom: 1rem;
                    color: #667eea;
                    font-size: 1.25rem;
                }
                
                #toc-list {
                    list-style: none;
                    padding: 0;
                }
                
                .toc-item {
                    margin: 0.5rem 0;
                }
                
                .toc-item a {
                    color: #4a5568;
                    text-decoration: none;
                    padding: 0.5rem 0;
                    display: block;
                    border-radius: 0.375rem;
                    transition: all 0.2s ease;
                }
                
                .toc-item a:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.1);
                    padding-left: 1rem;
                }
                
                .toc-h3 {
                    margin-left: 1rem;
                }
                
                .toc-h4 {
                    margin-left: 2rem;
                    font-size: 0.9rem;
                }
            `;
            document.head.appendChild(style);
        }
        
        return toc;
    }

    // 添加分享功能
    addShareButtons() {
        const shareContainer = document.createElement('div');
        shareContainer.className = 'share-buttons';
        shareContainer.innerHTML = `
            <h4>分享这篇博客</h4>
            <div class="share-btn-group">
                <button class="share-btn" data-platform="twitter">
                    <i class="fab fa-twitter"></i> Twitter
                </button>
                <button class="share-btn" data-platform="facebook">
                    <i class="fab fa-facebook"></i> Facebook
                </button>
                <button class="share-btn" data-platform="linkedin">
                    <i class="fab fa-linkedin"></i> LinkedIn
                </button>
                <button class="share-btn" data-platform="copy">
                    <i class="fas fa-link"></i> 复制链接
                </button>
            </div>
        `;
        
        // 添加分享按钮样式
        if (!document.querySelector('#share-styles')) {
            const style = document.createElement('style');
            style.id = 'share-styles';
            style.textContent = `
                .share-buttons {
                    background: #f8f9ff;
                    border-radius: 1rem;
                    padding: 2rem;
                    margin: 3rem 0;
                    text-align: center;
                }
                
                .share-buttons h4 {
                    margin-bottom: 1.5rem;
                    color: #4a5568;
                }
                
                .share-btn-group {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                    flex-wrap: wrap;
                }
                
                .share-btn {
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 2rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }
                
                .share-btn[data-platform="twitter"] {
                    background: #1da1f2;
                    color: white;
                }
                
                .share-btn[data-platform="facebook"] {
                    background: #1877f2;
                    color: white;
                }
                
                .share-btn[data-platform="linkedin"] {
                    background: #0a66c2;
                    color: white;
                }
                
                .share-btn[data-platform="copy"] {
                    background: #667eea;
                    color: white;
                }
                
                .share-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
                }
            `;
            document.head.appendChild(style);
        }
        
        // 绑定分享事件
        shareContainer.addEventListener('click', (e) => {
            const btn = e.target.closest('.share-btn');
            if (!btn) return;
            
            const platform = btn.dataset.platform;
            const url = window.location.href;
            const title = document.title;
            
            this.handleShare(platform, url, title);
        });
        
        return shareContainer;
    }

    handleShare(platform, url, title) {
        const shareUrls = {
            twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
            facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
            linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`
        };
        
        if (platform === 'copy') {
            navigator.clipboard.writeText(url).then(() => {
                this.showToast('链接已复制到剪贴板！', 'success');
            });
        } else if (shareUrls[platform]) {
            window.open(shareUrls[platform], '_blank', 'width=600,height=400');
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.textContent = message;
        
        // 添加 toast 样式
        if (!document.querySelector('#toast-styles')) {
            const style = document.createElement('style');
            style.id = 'toast-styles';
            style.textContent = `
                .toast-notification {
                    position: fixed;
                    bottom: 2rem;
                    left: 50%;
                    transform: translateX(-50%);
                    background: #333;
                    color: white;
                    padding: 1rem 2rem;
                    border-radius: 2rem;
                    z-index: 1000;
                    animation: toastSlideUp 0.3s ease;
                }
                
                .toast-success {
                    background: #10b981;
                }
                
                @keyframes toastSlideUp {
                    from {
                        opacity: 0;
                        transform: translateX(-50%) translateY(100%);
                    }
                    to {
                        opacity: 1;
                        transform: translateX(-50%) translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new BlogUtils();
});