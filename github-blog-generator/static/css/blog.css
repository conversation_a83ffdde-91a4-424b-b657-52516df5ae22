/* Static CSS for blog templates */

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Container */
.blog-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* Header styles */
.blog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 2rem;
    text-align: center;
    border-radius: 1rem;
    margin-bottom: 3rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.blog-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.blog-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    font-size: 1rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    backdrop-filter: blur(10px);
}

/* Main content */
.blog-content {
    background: white;
    border-radius: 1rem;
    padding: 3rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    margin-bottom: 3rem;
}

.section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid #667eea;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: #764ba2;
    border-radius: 2px;
}

.section-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #4a5568;
}

.section-content p {
    margin-bottom: 1.5rem;
}

.section-content ul, .section-content ol {
    margin: 1.5rem 0;
    padding-left: 2rem;
}

.section-content li {
    margin-bottom: 0.75rem;
}

/* Tech stack grid */
.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.tech-item {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    padding: 2rem;
    border-radius: 1rem;
    border-left: 4px solid #667eea;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.tech-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.tech-item h4 {
    color: #667eea;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.tech-item p {
    color: #64748b;
    font-size: 0.95rem;
}

/* Feature list */
.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    background: #f8f9ff;
    margin: 1rem 0;
    padding: 1.25rem;
    border-radius: 0.75rem;
    border-left: 4px solid #667eea;
    font-size: 1.1rem;
    transition: all 0.2s ease;
}

.feature-list li:hover {
    background: #f0f2ff;
    transform: translateX(4px);
}

/* Code blocks */
.code-block {
    background: #1a1a1a;
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    overflow-x: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.code-block pre {
    margin: 0;
    color: #e2e8f0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

.code-comment {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    color: #a0aec0;
    font-style: italic;
    border-left: 3px solid #667eea;
}

/* Project links */
.project-links {
    display: flex;
    gap: 1rem;
    margin: 2rem 0;
    justify-content: center;
    flex-wrap: wrap;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.project-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-item {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.5rem;
}

/* Footer */
.blog-footer {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border-top: 4px solid #667eea;
}

.footer-content {
    color: #64748b;
    font-size: 0.95rem;
}

.footer-links {
    margin-top: 1rem;
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: #764ba2;
}

/* Responsive design */
@media (max-width: 768px) {
    .blog-container {
        padding: 1rem 0.5rem;
    }
    
    .blog-header {
        padding: 3rem 1.5rem;
    }
    
    .blog-title {
        font-size: 2.25rem;
    }
    
    .blog-content {
        padding: 2rem 1.5rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .blog-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .tech-grid {
        grid-template-columns: 1fr;
    }
    
    .project-links {
        flex-direction: column;
        align-items: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .blog-title {
        font-size: 2rem;
    }
    
    .blog-content {
        padding: 1.5rem 1rem;
    }
    
    .section-title {
        font-size: 1.5rem;
    }
    
    .code-block {
        padding: 1.5rem 1rem;
    }
    
    .tech-item {
        padding: 1.5rem;
    }
}

/* Print styles */
@media print {
    .blog-header {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .project-link {
        background: #667eea !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .blog-container {
        max-width: none;
        padding: 0;
    }
    
    .section {
        break-inside: avoid;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .blog-header {
        background: #000;
        color: #fff;
    }
    
    .section-title {
        border-bottom-color: #000;
    }
    
    .tech-item {
        border-left-color: #000;
    }
}