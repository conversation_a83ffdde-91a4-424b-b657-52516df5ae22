:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #f8fafc;
    --accent-color: #10b981;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --border-color: #e5e7eb;
    --bg-white: #ffffff;
    --bg-gray: #f9fafb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Styles */
.header {
    padding: 3rem 0 2rem;
    text-align: center;
}

.header-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2.25rem;
}

.subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Main Content */
.main-content {
    padding-bottom: 4rem;
}

/* Input Section */
.input-section {
    margin-bottom: 4rem;
}

.input-container {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.input-container h2 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-align: center;
}

.github-form {
    max-width: 600px;
    margin: 0 auto;
}

.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.input-wrapper {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 1rem;
    z-index: 1;
}

.input-wrapper input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--bg-white);
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.generate-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.generate-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.generate-btn:active {
    transform: translateY(0);
}

/* Loading Section */
.loading-section {
    text-align: center;
    margin: 3rem 0;
}

.loading-container {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-container h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

#loading-text {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Preview Section */
.preview-section {
    margin: 3rem 0;
}

.preview-container {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.preview-container h2 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.preview-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background: var(--bg-white);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.action-btn.primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
}

.blog-preview {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
    background: var(--bg-gray);
    min-height: 400px;
}

/* Features Section */
.features-section {
    margin: 4rem 0;
}

.features-container {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.features-container h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    margin-top: 3rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.footer-content {
    padding: 2rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }
    
    .header {
        padding: 2rem 0 1.5rem;
    }
    
    .header-content {
        padding: 1.5rem;
    }
    
    .logo {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .logo i {
        font-size: 1.75rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .input-container {
        padding: 1.5rem;
    }
    
    .input-container h2 {
        font-size: 1.5rem;
    }
    
    .preview-actions {
        flex-direction: column;
    }
    
    .action-btn {
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 1rem;
    }
    
    .input-container {
        padding: 1rem;
    }
    
    .preview-container {
        padding: 1.5rem;
    }
    
    .features-container {
        padding: 2rem 1rem;
    }
    
    .blog-preview {
        padding: 1rem;
    }
}

/* Animation and Transitions */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Utility Classes */
.hidden {
    display: none;
}

.visible {
    display: block;
}

.text-center {
    text-align: center;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.p-2 {
    padding: 0.5rem;
}

.rounded {
    border-radius: var(--radius-md);
}