class GitHubBlogGenerator {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadingSteps = [
            '正在获取项目信息...',
            '正在分析代码结构...',
            '正在解析技术栈...',
            '正在生成博客内容...',
            '正在渲染页面...'
        ];
        this.currentStep = 0;
    }

    bindEvents() {
        const form = document.getElementById('github-form');
        const editBtn = document.getElementById('edit-btn');
        const downloadBtn = document.getElementById('download-btn');
        const shareBtn = document.getElementById('share-btn');

        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }

        if (editBtn) {
            editBtn.addEventListener('click', () => this.handleEdit());
        }

        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.handleDownload());
        }

        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.handleShare());
        }

        // 输入框实时验证
        const urlInput = document.getElementById('github-url');
        if (urlInput) {
            urlInput.addEventListener('input', (e) => this.validateGitHubUrl(e.target));
        }
    }

    validateGitHubUrl(input) {
        const url = input.value;
        const githubRegex = /^https?:\/\/(www\.)?github\.com\/[\w-]+\/[\w.-]+\/?$/;
        
        if (url && !githubRegex.test(url)) {
            input.style.borderColor = '#ef4444';
            this.showToast('请输入有效的 GitHub 项目 URL', 'error');
        } else {
            input.style.borderColor = '#e5e7eb';
        }
    }

    async handleFormSubmit(e) {
        e.preventDefault();
        
        const urlInput = document.getElementById('github-url');
        const titleInput = document.getElementById('blog-title');
        
        const githubUrl = urlInput.value.trim();
        const customTitle = titleInput.value.trim();

        if (!this.isValidGitHubUrl(githubUrl)) {
            this.showToast('请输入有效的 GitHub 项目 URL', 'error');
            return;
        }

        this.showLoading();
        
        try {
            // 调用后端API生成博客
            const response = await fetch('http://localhost:5000/api/generate-blog', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    github_url: githubUrl,
                    custom_title: customTitle
                })
            });
            
            this.hideLoading();
            
            if (response.ok) {
                const result = await response.json();
                
                if (result.success) {
                    // 成功生成博客，显示成功信息并跳转
                    this.showToast('博客生成成功！正在跳转到博客页面...', 'success');
                    
                    // 给用户一点时间看到成功消息，然后跳转
                    setTimeout(() => {
                        window.location.href = `http://localhost:5000${result.blog_url}`;
                    }, 2000);
                    
                } else {
                    this.showToast(result.error || '生成博客时出现错误', 'error');
                }
            } else {
                // 解析错误响应
                try {
                    const errorResult = await response.json();
                    this.showToast(errorResult.error || `服务器错误 (${response.status})`, 'error');
                } catch {
                    this.showToast(`服务器错误 (${response.status})`, 'error');
                }
            }
            
        } catch (error) {
            this.hideLoading();
            console.error('Error generating blog:', error);
            
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                this.showToast('无法连接到后端服务，请确保服务正在运行 (http://localhost:5000)', 'error');
            } else {
                this.showToast('网络错误，请稍后重试', 'error');
            }
        }
    }

    isValidGitHubUrl(url) {
        const githubRegex = /^https?:\/\/(www\.)?github\.com\/[\w-]+\/[\w.-]+\/?$/;
        return githubRegex.test(url);
    }

    showLoading() {
        document.getElementById('loading-section').style.display = 'block';
        document.getElementById('preview-section').style.display = 'none';
        
        // 添加动画效果
        document.getElementById('loading-section').classList.add('fade-in');
        
        this.startLoadingAnimation();
    }

    hideLoading() {
        document.getElementById('loading-section').style.display = 'none';
        this.currentStep = 0;
    }

    startLoadingAnimation() {
        const loadingText = document.getElementById('loading-text');
        
        const updateStep = () => {
            if (this.currentStep < this.loadingSteps.length) {
                loadingText.textContent = this.loadingSteps[this.currentStep];
                this.currentStep++;
                setTimeout(updateStep, 1000);
            }
        };
        
        updateStep();
    }

    // 移除了旧的模拟函数，现在使用真正的后端API

    showPreview(blogData) {
        // 这个函数现在不再需要，因为我们直接跳转到博客页面
        console.log('Redirecting to blog page instead of showing preview');
    }

    renderBlogContent(blogData) {
        return `
            <article class="blog-article">
                <header class="blog-header">
                    <h1 class="blog-title">${blogData.title}</h1>
                    <p class="blog-subtitle">${blogData.subtitle}</p>
                    <div class="blog-meta">
                        <span class="blog-author">
                            <i class="fas fa-user"></i>
                            ${blogData.author}
                        </span>
                        <span class="blog-date">
                            <i class="fas fa-calendar"></i>
                            ${blogData.date}
                        </span>
                        <a href="${blogData.githubUrl}" class="blog-github" target="_blank">
                            <i class="fab fa-github"></i>
                            GitHub
                        </a>
                    </div>
                </header>
                
                <div class="blog-content">
                    ${blogData.sections.map(section => `
                        <section class="blog-section">
                            <h2 class="section-title">${section.title}</h2>
                            <div class="section-content">${section.content}</div>
                        </section>
                    `).join('')}
                </div>
            </article>
            
            <style>
                .blog-article {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 12px;
                    padding: 2rem;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                
                .blog-header {
                    text-align: center;
                    margin-bottom: 3rem;
                    padding-bottom: 2rem;
                    border-bottom: 2px solid #f0f0f0;
                }
                
                .blog-title {
                    font-size: 2.5rem;
                    font-weight: 700;
                    color: #1a1a1a;
                    margin-bottom: 1rem;
                }
                
                .blog-subtitle {
                    font-size: 1.25rem;
                    color: #666;
                    margin-bottom: 1.5rem;
                }
                
                .blog-meta {
                    display: flex;
                    justify-content: center;
                    gap: 2rem;
                    flex-wrap: wrap;
                }
                
                .blog-meta span, .blog-meta a {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    color: #888;
                    text-decoration: none;
                    font-size: 0.9rem;
                }
                
                .blog-github:hover {
                    color: #6366f1;
                }
                
                .blog-section {
                    margin-bottom: 3rem;
                }
                
                .section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #2d3748;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 3px solid #6366f1;
                }
                
                .section-content {
                    line-height: 1.8;
                    color: #4a5568;
                }
                
                .section-content p {
                    margin-bottom: 1rem;
                }
                
                .tech-stack {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1.5rem;
                    margin: 1.5rem 0;
                }
                
                .tech-item {
                    background: #f8f9ff;
                    padding: 1.5rem;
                    border-radius: 8px;
                    border-left: 4px solid #6366f1;
                }
                
                .tech-item h4 {
                    color: #6366f1;
                    margin-bottom: 0.5rem;
                }
                
                .feature-list {
                    list-style: none;
                    padding: 0;
                }
                
                .feature-list li {
                    padding: 0.75rem 0;
                    border-bottom: 1px solid #eee;
                    font-size: 1.1rem;
                }
                
                .code-highlight {
                    background: #1a1a1a;
                    border-radius: 8px;
                    padding: 1.5rem;
                    margin: 1.5rem 0;
                }
                
                .code-highlight pre {
                    margin: 0;
                    color: #e2e8f0;
                    overflow-x: auto;
                }
                
                .code-highlight code {
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                    font-size: 0.9rem;
                }
                
                .code-highlight p {
                    margin-top: 1rem;
                    color: #a0aec0;
                    font-style: italic;
                }
                
                .project-links {
                    display: flex;
                    gap: 1rem;
                    margin-top: 2rem;
                    justify-content: center;
                }
                
                .project-link {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    background: #6366f1;
                    color: white;
                    text-decoration: none;
                    border-radius: 6px;
                    transition: background 0.2s;
                }
                
                .project-link:hover {
                    background: #4f46e5;
                }
                
                @media (max-width: 768px) {
                    .blog-article {
                        padding: 1.5rem;
                    }
                    
                    .blog-title {
                        font-size: 2rem;
                    }
                    
                    .blog-meta {
                        flex-direction: column;
                        gap: 1rem;
                    }
                    
                    .tech-stack {
                        grid-template-columns: 1fr;
                    }
                    
                    .project-links {
                        flex-direction: column;
                    }
                }
            </style>
        `;
    }

    handleEdit() {
        this.showToast('编辑功能开发中...', 'info');
    }

    handleDownload() {
        const blogContent = document.getElementById('blog-content');
        const content = blogContent.innerHTML;
        
        const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub 项目博客</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
            background: #f9fafb;
        }
    </style>
</head>
<body>
    ${content}
</body>
</html>`;

        const blob = new Blob([fullHtml], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'github-blog.html';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showToast('博客已下载！', 'success');
    }

    handleShare() {
        const url = window.location.href;
        
        if (navigator.share) {
            navigator.share({
                title: 'GitHub 项目博客',
                text: '查看这个精美的 GitHub 项目博客',
                url: url
            });
        } else {
            // 复制链接到剪贴板
            navigator.clipboard.writeText(url).then(() => {
                this.showToast('链接已复制到剪贴板！', 'success');
            }).catch(() => {
                this.showToast('分享功能暂不可用', 'error');
            });
        }
    }

    showToast(message, type = 'info') {
        // 移除现有的 toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="toast-icon ${this.getToastIcon(type)}"></i>
                <span class="toast-message">${message}</span>
            </div>
        `;

        // 添加 toast 样式
        const style = document.createElement('style');
        style.textContent = `
            .toast {
                position: fixed;
                top: 2rem;
                right: 2rem;
                background: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.15);
                z-index: 1000;
                animation: slideInRight 0.3s ease;
                border-left: 4px solid #6366f1;
            }
            
            .toast-success { border-left-color: #10b981; }
            .toast-error { border-left-color: #ef4444; }
            .toast-info { border-left-color: #3b82f6; }
            
            .toast-content {
                display: flex;
                align-items: center;
                gap: 0.75rem;
            }
            
            .toast-icon {
                font-size: 1.25rem;
            }
            
            .toast-success .toast-icon { color: #10b981; }
            .toast-error .toast-icon { color: #ef4444; }
            .toast-info .toast-icon { color: #3b82f6; }
            
            .toast-message {
                font-weight: 500;
                color: #374151;
            }
            
            @keyframes slideInRight {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            
            @media (max-width: 768px) {
                .toast {
                    top: 1rem;
                    right: 1rem;
                    left: 1rem;
                    padding: 0.75rem 1rem;
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
                style.remove();
            }
        }, 3000);
    }

    getToastIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new GitHubBlogGenerator();
});