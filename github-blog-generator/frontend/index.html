<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub 项目可视化博客生成器</title>
    <link rel="stylesheet" href="/frontend/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fab fa-github"></i>
                    GitHub 博客生成器
                </h1>
                <p class="subtitle">将您的 GitHub 项目转换为精美的技术博客</p>
            </div>
        </header>

        <main class="main-content">
            <section class="input-section">
                <div class="input-container">
                    <h2>开始创建您的博客</h2>
                    <form id="github-form" class="github-form">
                        <div class="input-group">
                            <label for="github-url">GitHub 项目 URL</label>
                            <div class="input-wrapper">
                                <i class="fab fa-github input-icon"></i>
                                <input 
                                    type="url" 
                                    id="github-url" 
                                    placeholder="https://github.com/username/repository"
                                    required
                                >
                            </div>
                        </div>
                        <div class="input-group">
                            <label for="blog-title">博客标题 (可选)</label>
                            <div class="input-wrapper">
                                <i class="fas fa-heading input-icon"></i>
                                <input 
                                    type="text" 
                                    id="blog-title" 
                                    placeholder="自定义博客标题"
                                >
                            </div>
                        </div>
                        <button type="submit" class="generate-btn">
                            <i class="fas fa-magic"></i>
                            生成博客
                        </button>
                    </form>
                </div>
            </section>

            <section class="loading-section" id="loading-section" style="display: none;">
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <h3>正在分析项目...</h3>
                    <p id="loading-text">正在获取项目信息</p>
                </div>
            </section>

            <section class="preview-section" id="preview-section" style="display: none;">
                <div class="preview-container">
                    <h2>博客预览</h2>
                    <div class="preview-actions">
                        <button class="action-btn" id="edit-btn">
                            <i class="fas fa-edit"></i>
                            编辑内容
                        </button>
                        <button class="action-btn primary" id="download-btn">
                            <i class="fas fa-download"></i>
                            下载博客
                        </button>
                        <button class="action-btn" id="share-btn">
                            <i class="fas fa-share"></i>
                            分享链接
                        </button>
                    </div>
                    <div class="blog-preview" id="blog-content">
                        <!-- 博客内容将在这里动态生成 -->
                    </div>
                </div>
            </section>

            <section class="features-section">
                <div class="features-container">
                    <h2>功能特色</h2>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3>智能分析</h3>
                            <p>自动分析 GitHub 项目结构、代码特点和技术栈</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h3>美观设计</h3>
                            <p>现代化响应式设计，支持多种主题风格</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <h3>代码高亮</h3>
                            <p>智能代码语法高亮和项目结构可视化</p>
                        </div>
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <h3>响应式</h3>
                            <p>完美适配桌面端、平板和移动设备</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2024 GitHub 博客生成器. 让技术分享更简单.</p>
                <div class="footer-links">
                    <a href="#" class="footer-link">关于我们</a>
                    <a href="#" class="footer-link">使用指南</a>
                    <a href="#" class="footer-link">反馈建议</a>
                </div>
            </div>
        </footer>
    </div>

    <script src="/frontend/script.js"></script>
</body>
</html>