#!/usr/bin/env python3
"""
状态检查脚本 - 检查服务器是否正常运行
"""

import requests
import json
import time

def check_server_status():
    """检查服务器状态"""
    try:
        print("🔍 检查服务器状态...")
        
        # 检查主页
        response = requests.get('http://localhost:5000', timeout=5)
        print(f"主页状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 服务器正常运行")
            print("📍 可以访问: http://localhost:5000")
            
            # 测试API
            print("\n🧪 测试API功能...")
            api_data = {
                'github_url': 'https://github.com/torvalds/linux',
                'custom_title': '测试博客'
            }
            
            api_response = requests.post(
                'http://localhost:5000/api/generate-blog',
                json=api_data,
                timeout=30
            )
            
            print(f"API状态码: {api_response.status_code}")
            
            if api_response.status_code == 200:
                result = api_response.json()
                print("✅ API功能正常")
                print(f"博客URL: {result.get('blog_url', 'N/A')}")
                
                # 写入状态文件
                with open('server_status.txt', 'w') as f:
                    f.write("SERVER_STATUS=RUNNING\n")
                    f.write(f"TIMESTAMP={time.time()}\n")
                    f.write("MAIN_PAGE=OK\n")
                    f.write("API=OK\n")
                    f.write("URL=http://localhost:5000\n")
                
                return True
            else:
                print(f"❌ API功能异常: {api_response.text[:100]}")
                
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请确保服务器正在运行")
        
        # 写入错误状态
        with open('server_status.txt', 'w') as f:
            f.write("SERVER_STATUS=NOT_RUNNING\n")
            f.write(f"TIMESTAMP={time.time()}\n")
            f.write("ERROR=CONNECTION_REFUSED\n")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        
        # 写入错误状态
        with open('server_status.txt', 'w') as f:
            f.write("SERVER_STATUS=ERROR\n")
            f.write(f"TIMESTAMP={time.time()}\n")
            f.write(f"ERROR={str(e)}\n")
    
    return False

if __name__ == "__main__":
    print("🚀 GitHub博客生成器 - 状态检查")
    print("=" * 50)
    
    success = check_server_status()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 服务器运行正常！")
        print("🌐 请在浏览器中访问: http://localhost:5000")
    else:
        print("⚠️ 服务器可能未正常启动")
        print("💡 请检查终端输出或重新启动服务")
    
    print("📄 状态已保存到 server_status.txt")
