@echo off
REM GitHub博客生成器启动脚本
REM 双击此文件即可启动

echo 🚀 启动GitHub博客生成器...
echo ================================

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装Python
    pause
    exit /b 1
)

echo ✅ Python 已安装

REM 检查依赖
echo 📦 检查依赖...
python -c "import flask, requests, matplotlib, seaborn, pandas" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 正在安装依赖...
    pip install -r requirements.txt
)

echo ✅ 依赖检查完成

REM 启动服务器
echo 🚀 启动服务器...
echo 📍 服务地址: http://localhost:5000
echo 💡 服务启动后会自动打开浏览器
echo 💡 按 Ctrl+C 停止服务
echo ================================

REM 启动Flask应用
python -c "
import os
import sys
import webbrowser
import time
from threading import Timer

# 添加backend到路径
sys.path.insert(0, 'backend')

try:
    from backend.app import app
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        webbrowser.open('http://localhost:5000')
    
    Timer(1, open_browser).start()
    
    print('✅ 服务器启动成功！')
    app.run(host='0.0.0.0', port=5000, debug=False)
    
except Exception as e:
    print(f'❌ 启动失败: {e}')
    input('按回车键退出...')
"

echo 🛑 服务已停止
pause
