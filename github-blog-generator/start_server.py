#!/usr/bin/env python3
"""
简单的服务器启动脚本
直接启动Flask应用，避免复杂的启动逻辑
"""

import os
import sys
import subprocess

def main():
    """启动Flask服务器"""
    print("🚀 启动GitHub博客生成器...")
    print("=" * 50)
    
    # 切换到backend目录
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    os.chdir(backend_dir)
    
    # 设置环境变量
    env = os.environ.copy()
    env['FLASK_APP'] = 'app.py'
    env['FLASK_ENV'] = 'development'
    env['FLASK_DEBUG'] = '1'
    
    print("📍 服务地址: http://localhost:5000")
    print("📍 API地址: http://localhost:5000/api/")
    print("💡 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        # 启动Flask应用
        subprocess.run([
            sys.executable, '-m', 'flask', 'run',
            '--host', '0.0.0.0',
            '--port', '5000'
        ], env=env, check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n💡 请尝试手动运行:")
        print("cd backend")
        print("python3 app.py")

if __name__ == "__main__":
    main()
