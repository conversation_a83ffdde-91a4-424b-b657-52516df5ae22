#!/usr/bin/env python3
"""
直接启动脚本 - 最简单的启动方式
"""

import os
import sys

def main():
    print("🚀 直接启动GitHub博客生成器...")
    
    # 切换到backend目录
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    
    # 添加backend到Python路径
    sys.path.insert(0, backend_dir)
    
    # 切换工作目录
    os.chdir(os.path.dirname(__file__))
    
    try:
        # 直接导入并运行Flask应用
        from backend.app import app
        
        print("✅ Flask应用加载成功")
        print("📍 服务地址: http://localhost:5000")
        print("💡 按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 启动Flask开发服务器
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False  # 避免重载问题
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请检查依赖是否安装: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查端口5000是否被占用")

if __name__ == "__main__":
    main()
