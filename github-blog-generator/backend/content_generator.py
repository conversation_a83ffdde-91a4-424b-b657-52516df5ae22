import json
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from datetime import datetime, timezone
import io
import base64
from typing import Dict, List
import os

class ContentGenerator:
    def __init__(self):
        try:
            plt.style.use('seaborn-v0_8')
        except OSError:
            # Fallback to default style if seaborn style is not available
            plt.style.use('default')
        sns.set_palette("husl")
    
    def generate_blog_post(self, repo_data: Dict) -> Dict:
        basic_info = repo_data['basic_info']
        
        # Generate different sections
        intro = self._generate_introduction(basic_info)
        stats = self._generate_statistics(repo_data)
        activity = self._generate_activity_analysis(repo_data)
        tech_stack = self._generate_tech_stack_analysis(repo_data)
        contributors_analysis = self._generate_contributors_analysis(repo_data)
        visualizations = self._generate_visualizations(repo_data)
        
        return {
            'title': f"Deep Dive into {basic_info['name']}: A GitHub Repository Analysis",
            'introduction': intro,
            'statistics': stats,
            'activity_analysis': activity,
            'tech_stack': tech_stack,
            'contributors_analysis': contributors_analysis,
            'visualizations': visualizations,
            'generated_at': datetime.now().isoformat()
        }
    
    def _generate_introduction(self, basic_info: Dict) -> str:
        created_date = datetime.fromisoformat(basic_info['created_at'].replace('Z', '+00:00'))
        now_with_tz = datetime.now(timezone.utc)
        age_days = (now_with_tz - created_date).days

        name = basic_info['name']
        language = basic_info['language']
        stars = basic_info['stars']
        description = basic_info['description'] or 'No description provided'

        # 根据项目规模和技术栈生成深度分析
        project_scale = self._analyze_project_scale(stars, basic_info['forks'])
        tech_significance = self._analyze_tech_significance(language)

        intro = f"""
# {name} - 深度技术解析

## 🎯 项目概述

**{name}** 是一个{project_scale}的开源项目，{description}

该项目采用 **{language}** 作为主要开发语言，{tech_significance}在GitHub上已获得 **{stars:,}** 个星标，展现了其在开发者社区中的{self._get_popularity_level(stars)}。

### 📊 项目核心指标

| 指标 | 数值 | 分析 |
|------|------|------|
| ⭐ Stars | {stars:,} | {self._analyze_stars(stars)} |
| 🍴 Forks | {basic_info['forks']:,} | {self._analyze_forks(basic_info['forks'], stars)} |
| 👀 Watchers | {basic_info['watchers']:,} | {self._analyze_watchers(basic_info['watchers'])} |
| 🐛 Open Issues | {basic_info['open_issues']:,} | {self._analyze_issues(basic_info['open_issues'])} |
| 📅 项目年龄 | {age_days} 天 | {self._analyze_age(age_days)} |
| 📝 开源协议 | {basic_info['license']} | {self._analyze_license(basic_info['license'])} |

### 🔍 为什么值得深入研究？

{self._generate_research_value(basic_info)}
        """

        return intro.strip()

    def _analyze_project_scale(self, stars: int, forks: int) -> str:
        if stars > 50000:
            return "大型企业级"
        elif stars > 10000:
            return "知名开源"
        elif stars > 1000:
            return "活跃社区"
        else:
            return "新兴"

    def _analyze_tech_significance(self, language: str) -> str:
        tech_insights = {
            'JavaScript': '体现了现代前端开发的技术趋势，',
            'Python': '展示了Python在现代软件开发中的强大能力，',
            'Java': '代表了企业级应用开发的最佳实践，',
            'TypeScript': '体现了类型安全在大型项目中的重要性，',
            'Go': '展现了云原生时代的高性能服务开发理念，',
            'Rust': '代表了系统级编程的安全性和性能追求，',
            'C++': '体现了高性能计算和系统编程的技术深度，',
        }
        return tech_insights.get(language, f'展示了{language}技术栈的实际应用，')

    def _get_popularity_level(self, stars: int) -> str:
        if stars > 50000:
            return "巨大影响力和广泛认可"
        elif stars > 10000:
            return "强大的社区支持和认可度"
        elif stars > 1000:
            return "良好的社区反响"
        else:
            return "初步的社区关注"

    def _analyze_stars(self, stars: int) -> str:
        if stars > 50000:
            return "顶级开源项目，业界标杆"
        elif stars > 10000:
            return "知名项目，值得深入学习"
        elif stars > 1000:
            return "活跃项目，有一定影响力"
        else:
            return "新兴项目，潜力待发掘"

    def _analyze_forks(self, forks: int, stars: int) -> str:
        ratio = forks / max(stars, 1)
        if ratio > 0.3:
            return "高Fork率，开发者参与度很高"
        elif ratio > 0.1:
            return "适中的Fork率，有一定开发者参与"
        else:
            return "较低Fork率，主要用于学习参考"

    def _analyze_watchers(self, watchers: int) -> str:
        if watchers > 1000:
            return "高关注度，项目动态备受关注"
        elif watchers > 100:
            return "适中关注度，有稳定的关注群体"
        else:
            return "基础关注度，小众但专业"

    def _analyze_issues(self, issues: int) -> str:
        if issues > 500:
            return "活跃的问题讨论，社区参与度高"
        elif issues > 50:
            return "适量的问题反馈，项目在持续改进"
        else:
            return "较少的开放问题，项目相对稳定"

    def _analyze_age(self, days: int) -> str:
        years = days / 365
        if years > 5:
            return "成熟项目，经过长期验证"
        elif years > 2:
            return "稳定项目，有一定发展历史"
        elif years > 1:
            return "发展中项目，正在快速迭代"
        else:
            return "新项目，处于早期发展阶段"

    def _analyze_license(self, license_name: str) -> str:
        license_analysis = {
            'MIT': '宽松的MIT协议，商业友好',
            'Apache-2.0': 'Apache协议，企业级开源标准',
            'GPL-3.0': 'GPL协议，强制开源传播',
            'BSD-3-Clause': 'BSD协议，简洁宽松',
            'No license': '无明确协议，使用需谨慎'
        }
        return license_analysis.get(license_name, f'{license_name}协议')

    def _generate_research_value(self, basic_info: Dict) -> str:
        values = []

        if basic_info['stars'] > 10000:
            values.append("**高影响力**：项目在业界具有重要地位，代表了技术发展方向")

        if basic_info['language'] in ['JavaScript', 'Python', 'TypeScript', 'Go', 'Rust']:
            values.append(f"**技术前沿**：{basic_info['language']}是当前热门技术栈，学习价值很高")

        if basic_info['forks'] > 1000:
            values.append("**活跃开发**：大量开发者参与，可以学习协作开发模式")

        if basic_info['license'] in ['MIT', 'Apache-2.0']:
            values.append("**商业友好**：开源协议允许商业使用，实用价值高")

        if not values:
            values.append("**学习价值**：作为开源项目，可以学习代码组织和项目管理经验")

        return '\n'.join(f"{i+1}. {value}" for i, value in enumerate(values))
    
    def _generate_statistics(self, repo_data: Dict) -> Dict:
        basic_info = repo_data['basic_info']
        issues = repo_data['issues']
        
        stats = {
            'engagement_score': self._calculate_engagement_score(basic_info),
            'activity_level': self._calculate_activity_level(repo_data),
            'community_health': self._calculate_community_health(repo_data),
            'issue_resolution_rate': self._calculate_issue_resolution_rate(issues)
        }
        
        return stats
    
    def _generate_activity_analysis(self, repo_data: Dict) -> str:
        commits = repo_data['commits']
        
        if not commits:
            return "No recent commit activity found."
        
        # Analyze commit patterns
        commit_dates = [datetime.fromisoformat(commit['date'].replace('Z', '+00:00')) for commit in commits]
        now_with_tz = datetime.now(timezone.utc)
        recent_commits = len([d for d in commit_dates if (now_with_tz - d).days <= 30])
        
        activity_analysis = f"""
        ## Recent Activity Analysis
        
        **Last {len(commits)} Commits:**
        - 📊 **Recent commits (last 30 days):** {recent_commits}
        - 🔄 **Latest commit:** {commits[0]['message']} by {commits[0]['author']}
        - 👥 **Active contributors:** {len(set(commit['author'] for commit in commits))}
        
        ### Recent Commit Messages:
        """
        
        for commit in commits[:5]:
            commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
            activity_analysis += f"- `{commit['sha']}` **{commit['message']}** - {commit['author']} ({commit_date.strftime('%b %d, %Y')})\n"
        
        return activity_analysis
    
    def _generate_tech_stack_analysis(self, repo_data: Dict) -> str:
        languages = repo_data['languages']
        
        if not languages:
            return "No language data available."
        
        total_bytes = sum(languages.values())
        
        tech_analysis = f"""
        ## Technology Stack Analysis
        
        **Primary Languages Used:**
        
        """
        
        for lang, bytes_count in sorted(languages.items(), key=lambda x: x[1], reverse=True):
            percentage = (bytes_count / total_bytes) * 100
            tech_analysis += f"- **{lang}:** {percentage:.1f}% ({bytes_count:,} bytes)\n"
        
        # Add technology insights
        main_lang = max(languages.keys(), key=lambda x: languages[x])
        tech_analysis += f"""
        
        ### Technology Insights:
        - 🚀 **Primary Language:** {main_lang}
        - 📦 **Language Diversity:** {len(languages)} different languages
        - 🏗️ **Architecture:** {"Multi-language" if len(languages) > 3 else "Single-language focused"}
        """
        
        return tech_analysis
    
    def _generate_contributors_analysis(self, repo_data: Dict) -> str:
        contributors = repo_data['contributors']
        
        if not contributors:
            return "No contributor data available."
        
        total_contributions = sum(c['contributions'] for c in contributors)
        
        analysis = f"""
        ## Contributors Analysis
        
        **Top Contributors:**
        
        """
        
        for i, contributor in enumerate(contributors[:5], 1):
            percentage = (contributor['contributions'] / total_contributions) * 100
            analysis += f"{i}. **{contributor['login']}** - {contributor['contributions']} contributions ({percentage:.1f}%)\n"
        
        analysis += f"""
        
        ### Community Insights:
        - 👥 **Total Contributors:** {len(contributors)}
        - 💪 **Most Active:** {contributors[0]['login']} with {contributors[0]['contributions']} contributions
        - 🤝 **Collaboration Level:** {"High" if len(contributors) > 10 else "Medium" if len(contributors) > 3 else "Low"}
        """
        
        return analysis
    
    def _generate_visualizations(self, repo_data: Dict) -> Dict:
        visualizations = {}
        
        # Language distribution chart
        if repo_data['languages']:
            visualizations['language_chart'] = self._create_language_chart(repo_data['languages'])
        
        # Contributor distribution chart
        if repo_data['contributors']:
            visualizations['contributor_chart'] = self._create_contributor_chart(repo_data['contributors'])
        
        # Activity timeline
        if repo_data['commits']:
            visualizations['activity_timeline'] = self._create_activity_timeline(repo_data['commits'])
        
        return visualizations
    
    def _create_language_chart(self, languages: Dict) -> str:
        plt.figure(figsize=(10, 6))
        
        # Prepare data
        langs = list(languages.keys())
        sizes = list(languages.values())
        
        # Create pie chart
        plt.pie(sizes, labels=langs, autopct='%1.1f%%', startangle=90)
        plt.title('Language Distribution')
        plt.axis('equal')
        
        # Save to base64
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close()
        
        return f"data:image/png;base64,{img_str}"
    
    def _create_contributor_chart(self, contributors: List[Dict]) -> str:
        plt.figure(figsize=(12, 6))
        
        # Take top 10 contributors
        top_contributors = contributors[:10]
        names = [c['login'] for c in top_contributors]
        contributions = [c['contributions'] for c in top_contributors]
        
        # Create bar chart
        plt.barh(names, contributions)
        plt.xlabel('Contributions')
        plt.title('Top Contributors')
        plt.tight_layout()
        
        # Save to base64
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close()
        
        return f"data:image/png;base64,{img_str}"
    
    def _create_activity_timeline(self, commits: List[Dict]) -> str:
        plt.figure(figsize=(12, 6))
        
        # Prepare data
        dates = [datetime.fromisoformat(commit['date'].replace('Z', '+00:00')) for commit in commits]
        
        # Create timeline
        plt.plot(dates, range(len(dates)), marker='o')
        plt.xlabel('Date')
        plt.ylabel('Commit Index')
        plt.title('Recent Commit Activity')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Save to base64
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close()
        
        return f"data:image/png;base64,{img_str}"
    
    def _calculate_engagement_score(self, basic_info: Dict) -> float:
        # Simple engagement score based on stars, forks, and watchers
        score = (basic_info['stars'] * 0.5 + 
                basic_info['forks'] * 0.3 + 
                basic_info['watchers'] * 0.2)
        return round(score, 2)
    
    def _calculate_activity_level(self, repo_data: Dict) -> str:
        commits = repo_data['commits']
        if not commits:
            return "Low"
        
        # Check commits in last 30 days
        now_with_tz = datetime.now(timezone.utc)
        recent_commits = len([
            commit for commit in commits 
            if (now_with_tz - datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))).days <= 30
        ])
        
        if recent_commits >= 10:
            return "Very High"
        elif recent_commits >= 5:
            return "High"
        elif recent_commits >= 2:
            return "Medium"
        else:
            return "Low"
    
    def _calculate_community_health(self, repo_data: Dict) -> str:
        contributors = repo_data['contributors']
        basic_info = repo_data['basic_info']
        
        if not contributors:
            return "Unknown"
        
        # Consider number of contributors and distribution
        num_contributors = len(contributors)
        has_license = basic_info['license'] != 'No license'
        has_description = bool(basic_info['description'])
        
        score = 0
        if num_contributors > 10:
            score += 3
        elif num_contributors > 5:
            score += 2
        elif num_contributors > 1:
            score += 1
        
        if has_license:
            score += 1
        if has_description:
            score += 1
        
        if score >= 4:
            return "Excellent"
        elif score >= 3:
            return "Good"
        elif score >= 2:
            return "Fair"
        else:
            return "Needs Improvement"
    
    def _calculate_issue_resolution_rate(self, issues: Dict) -> float:
        total_issues = issues.get('total_issues', 0)
        if total_issues == 0:
            return 0.0
        
        closed_issues = issues.get('closed_issues', 0)
        return round((closed_issues / total_issues) * 100, 1)