import json
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from datetime import datetime, timezone
import io
import base64
from typing import Dict, List
import os

class ContentGenerator:
    def __init__(self):
        try:
            plt.style.use('seaborn-v0_8')
        except OSError:
            # Fallback to default style if seaborn style is not available
            plt.style.use('default')
        sns.set_palette("husl")
    
    def generate_blog_post(self, repo_data: Dict) -> Dict:
        basic_info = repo_data['basic_info']
        
        # Generate different sections
        intro = self._generate_introduction(basic_info)
        stats = self._generate_statistics(repo_data)
        activity = self._generate_activity_analysis(repo_data)
        tech_stack = self._generate_tech_stack_analysis(repo_data)
        contributors_analysis = self._generate_contributors_analysis(repo_data)
        visualizations = self._generate_visualizations(repo_data)
        
        return {
            'title': f"Deep Dive into {basic_info['name']}: A GitHub Repository Analysis",
            'introduction': intro,
            'statistics': stats,
            'activity_analysis': activity,
            'tech_stack': tech_stack,
            'contributors_analysis': contributors_analysis,
            'visualizations': visualizations,
            'generated_at': datetime.now().isoformat()
        }
    
    def _generate_introduction(self, basic_info: Dict) -> str:
        created_date = datetime.fromisoformat(basic_info['created_at'].replace('Z', '+00:00'))
        now_with_tz = datetime.now(timezone.utc)
        age_days = (now_with_tz - created_date).days
        
        intro = f"""
        # {basic_info['name']} - Project Overview
        
        **Repository:** {basic_info['full_name']}
        **Description:** {basic_info['description'] or 'No description provided'}
        
        ## Quick Stats
        - ⭐ **Stars:** {basic_info['stars']:,}
        - 🍴 **Forks:** {basic_info['forks']:,}
        - 👀 **Watchers:** {basic_info['watchers']:,}
        - 🐛 **Open Issues:** {basic_info['open_issues']:,}
        - 📅 **Created:** {created_date.strftime('%B %d, %Y')} ({age_days} days ago)
        - 📝 **License:** {basic_info['license']}
        - 🔧 **Primary Language:** {basic_info['language']}
        
        ## Repository Health
        This repository has been {"actively maintained" if age_days < 365 else "stable"} with a {"strong" if basic_info['stars'] > 100 else "growing"} community presence.
        """
        
        return intro
    
    def _generate_statistics(self, repo_data: Dict) -> Dict:
        basic_info = repo_data['basic_info']
        issues = repo_data['issues']
        
        stats = {
            'engagement_score': self._calculate_engagement_score(basic_info),
            'activity_level': self._calculate_activity_level(repo_data),
            'community_health': self._calculate_community_health(repo_data),
            'issue_resolution_rate': self._calculate_issue_resolution_rate(issues)
        }
        
        return stats
    
    def _generate_activity_analysis(self, repo_data: Dict) -> str:
        commits = repo_data['commits']
        
        if not commits:
            return "No recent commit activity found."
        
        # Analyze commit patterns
        commit_dates = [datetime.fromisoformat(commit['date'].replace('Z', '+00:00')) for commit in commits]
        now_with_tz = datetime.now(timezone.utc)
        recent_commits = len([d for d in commit_dates if (now_with_tz - d).days <= 30])
        
        activity_analysis = f"""
        ## Recent Activity Analysis
        
        **Last {len(commits)} Commits:**
        - 📊 **Recent commits (last 30 days):** {recent_commits}
        - 🔄 **Latest commit:** {commits[0]['message']} by {commits[0]['author']}
        - 👥 **Active contributors:** {len(set(commit['author'] for commit in commits))}
        
        ### Recent Commit Messages:
        """
        
        for commit in commits[:5]:
            commit_date = datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))
            activity_analysis += f"- `{commit['sha']}` **{commit['message']}** - {commit['author']} ({commit_date.strftime('%b %d, %Y')})\n"
        
        return activity_analysis
    
    def _generate_tech_stack_analysis(self, repo_data: Dict) -> str:
        languages = repo_data['languages']
        
        if not languages:
            return "No language data available."
        
        total_bytes = sum(languages.values())
        
        tech_analysis = f"""
        ## Technology Stack Analysis
        
        **Primary Languages Used:**
        
        """
        
        for lang, bytes_count in sorted(languages.items(), key=lambda x: x[1], reverse=True):
            percentage = (bytes_count / total_bytes) * 100
            tech_analysis += f"- **{lang}:** {percentage:.1f}% ({bytes_count:,} bytes)\n"
        
        # Add technology insights
        main_lang = max(languages.keys(), key=lambda x: languages[x])
        tech_analysis += f"""
        
        ### Technology Insights:
        - 🚀 **Primary Language:** {main_lang}
        - 📦 **Language Diversity:** {len(languages)} different languages
        - 🏗️ **Architecture:** {"Multi-language" if len(languages) > 3 else "Single-language focused"}
        """
        
        return tech_analysis
    
    def _generate_contributors_analysis(self, repo_data: Dict) -> str:
        contributors = repo_data['contributors']
        
        if not contributors:
            return "No contributor data available."
        
        total_contributions = sum(c['contributions'] for c in contributors)
        
        analysis = f"""
        ## Contributors Analysis
        
        **Top Contributors:**
        
        """
        
        for i, contributor in enumerate(contributors[:5], 1):
            percentage = (contributor['contributions'] / total_contributions) * 100
            analysis += f"{i}. **{contributor['login']}** - {contributor['contributions']} contributions ({percentage:.1f}%)\n"
        
        analysis += f"""
        
        ### Community Insights:
        - 👥 **Total Contributors:** {len(contributors)}
        - 💪 **Most Active:** {contributors[0]['login']} with {contributors[0]['contributions']} contributions
        - 🤝 **Collaboration Level:** {"High" if len(contributors) > 10 else "Medium" if len(contributors) > 3 else "Low"}
        """
        
        return analysis
    
    def _generate_visualizations(self, repo_data: Dict) -> Dict:
        visualizations = {}
        
        # Language distribution chart
        if repo_data['languages']:
            visualizations['language_chart'] = self._create_language_chart(repo_data['languages'])
        
        # Contributor distribution chart
        if repo_data['contributors']:
            visualizations['contributor_chart'] = self._create_contributor_chart(repo_data['contributors'])
        
        # Activity timeline
        if repo_data['commits']:
            visualizations['activity_timeline'] = self._create_activity_timeline(repo_data['commits'])
        
        return visualizations
    
    def _create_language_chart(self, languages: Dict) -> str:
        plt.figure(figsize=(10, 6))
        
        # Prepare data
        langs = list(languages.keys())
        sizes = list(languages.values())
        
        # Create pie chart
        plt.pie(sizes, labels=langs, autopct='%1.1f%%', startangle=90)
        plt.title('Language Distribution')
        plt.axis('equal')
        
        # Save to base64
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close()
        
        return f"data:image/png;base64,{img_str}"
    
    def _create_contributor_chart(self, contributors: List[Dict]) -> str:
        plt.figure(figsize=(12, 6))
        
        # Take top 10 contributors
        top_contributors = contributors[:10]
        names = [c['login'] for c in top_contributors]
        contributions = [c['contributions'] for c in top_contributors]
        
        # Create bar chart
        plt.barh(names, contributions)
        plt.xlabel('Contributions')
        plt.title('Top Contributors')
        plt.tight_layout()
        
        # Save to base64
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close()
        
        return f"data:image/png;base64,{img_str}"
    
    def _create_activity_timeline(self, commits: List[Dict]) -> str:
        plt.figure(figsize=(12, 6))
        
        # Prepare data
        dates = [datetime.fromisoformat(commit['date'].replace('Z', '+00:00')) for commit in commits]
        
        # Create timeline
        plt.plot(dates, range(len(dates)), marker='o')
        plt.xlabel('Date')
        plt.ylabel('Commit Index')
        plt.title('Recent Commit Activity')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Save to base64
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        img_str = base64.b64encode(img_buffer.read()).decode()
        plt.close()
        
        return f"data:image/png;base64,{img_str}"
    
    def _calculate_engagement_score(self, basic_info: Dict) -> float:
        # Simple engagement score based on stars, forks, and watchers
        score = (basic_info['stars'] * 0.5 + 
                basic_info['forks'] * 0.3 + 
                basic_info['watchers'] * 0.2)
        return round(score, 2)
    
    def _calculate_activity_level(self, repo_data: Dict) -> str:
        commits = repo_data['commits']
        if not commits:
            return "Low"
        
        # Check commits in last 30 days
        now_with_tz = datetime.now(timezone.utc)
        recent_commits = len([
            commit for commit in commits 
            if (now_with_tz - datetime.fromisoformat(commit['date'].replace('Z', '+00:00'))).days <= 30
        ])
        
        if recent_commits >= 10:
            return "Very High"
        elif recent_commits >= 5:
            return "High"
        elif recent_commits >= 2:
            return "Medium"
        else:
            return "Low"
    
    def _calculate_community_health(self, repo_data: Dict) -> str:
        contributors = repo_data['contributors']
        basic_info = repo_data['basic_info']
        
        if not contributors:
            return "Unknown"
        
        # Consider number of contributors and distribution
        num_contributors = len(contributors)
        has_license = basic_info['license'] != 'No license'
        has_description = bool(basic_info['description'])
        
        score = 0
        if num_contributors > 10:
            score += 3
        elif num_contributors > 5:
            score += 2
        elif num_contributors > 1:
            score += 1
        
        if has_license:
            score += 1
        if has_description:
            score += 1
        
        if score >= 4:
            return "Excellent"
        elif score >= 3:
            return "Good"
        elif score >= 2:
            return "Fair"
        else:
            return "Needs Improvement"
    
    def _calculate_issue_resolution_rate(self, issues: Dict) -> float:
        total_issues = issues.get('total_issues', 0)
        if total_issues == 0:
            return 0.0
        
        closed_issues = issues.get('closed_issues', 0)
        return round((closed_issues / total_issues) * 100, 1)