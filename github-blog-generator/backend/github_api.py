import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class GitHubAPI:
    def __init__(self, token: Optional[str] = None):
        self.token = token
        self.base_url = "https://api.github.com"
        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'GitHub-Blog-Generator/1.0'
        }
        if token:
            self.headers['Authorization'] = f'token {token}'
    
    def _make_request(self, url: str) -> Dict:
        response = requests.get(url, headers=self.headers)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API request failed: {response.status_code} - {response.text}")
    
    def get_repository_data(self, owner: str, repo: str) -> Dict:
        repo_url = f"{self.base_url}/repos/{owner}/{repo}"
        repo_data = self._make_request(repo_url)
        
        # Get additional data
        commits = self.get_recent_commits(owner, repo)
        contributors = self.get_contributors(owner, repo)
        languages = self.get_languages(owner, repo)
        issues = self.get_issues_stats(owner, repo)
        
        return {
            'basic_info': {
                'name': repo_data['name'],
                'full_name': repo_data['full_name'],
                'description': repo_data.get('description', ''),
                'url': repo_data['html_url'],
                'created_at': repo_data['created_at'],
                'updated_at': repo_data['updated_at'],
                'stars': repo_data['stargazers_count'],
                'forks': repo_data['forks_count'],
                'watchers': repo_data['watchers_count'],
                'language': repo_data.get('language', 'Unknown'),
                'size': repo_data['size'],
                'open_issues': repo_data['open_issues_count'],
                'has_wiki': repo_data['has_wiki'],
                'has_pages': repo_data['has_pages'],
                'license': repo_data.get('license', {}).get('name', 'No license') if repo_data.get('license') else 'No license'
            },
            'commits': commits,
            'contributors': contributors,
            'languages': languages,
            'issues': issues
        }
    
    def get_recent_commits(self, owner: str, repo: str, limit: int = 10) -> List[Dict]:
        url = f"{self.base_url}/repos/{owner}/{repo}/commits?per_page={limit}"
        commits_data = self._make_request(url)
        
        commits = []
        for commit in commits_data:
            commits.append({
                'sha': commit['sha'][:7],
                'message': commit['commit']['message'].split('\n')[0],
                'author': commit['commit']['author']['name'],
                'date': commit['commit']['author']['date'],
                'url': commit['html_url']
            })
        
        return commits
    
    def get_contributors(self, owner: str, repo: str) -> List[Dict]:
        url = f"{self.base_url}/repos/{owner}/{repo}/contributors"
        try:
            contributors_data = self._make_request(url)
            
            contributors = []
            for contributor in contributors_data[:10]:  # Top 10 contributors
                contributors.append({
                    'login': contributor['login'],
                    'contributions': contributor['contributions'],
                    'avatar_url': contributor['avatar_url'],
                    'url': contributor['html_url']
                })
            
            return contributors
        except:
            return []
    
    def get_languages(self, owner: str, repo: str) -> Dict:
        url = f"{self.base_url}/repos/{owner}/{repo}/languages"
        try:
            return self._make_request(url)
        except:
            return {}
    
    def get_issues_stats(self, owner: str, repo: str) -> Dict:
        try:
            # Get open issues
            open_issues_url = f"{self.base_url}/repos/{owner}/{repo}/issues?state=open&per_page=1"
            open_issues_response = requests.get(open_issues_url, headers=self.headers)
            open_issues_count = 0
            if open_issues_response.status_code == 200:
                # Get count from Link header
                link_header = open_issues_response.headers.get('Link', '')
                if 'last' in link_header:
                    import re
                    match = re.search(r'page=(\d+)>; rel="last"', link_header)
                    if match:
                        open_issues_count = int(match.group(1))
            
            # Get closed issues
            closed_issues_url = f"{self.base_url}/repos/{owner}/{repo}/issues?state=closed&per_page=1"
            closed_issues_response = requests.get(closed_issues_url, headers=self.headers)
            closed_issues_count = 0
            if closed_issues_response.status_code == 200:
                link_header = closed_issues_response.headers.get('Link', '')
                if 'last' in link_header:
                    import re
                    match = re.search(r'page=(\d+)>; rel="last"', link_header)
                    if match:
                        closed_issues_count = int(match.group(1))
            
            return {
                'open_issues': open_issues_count,
                'closed_issues': closed_issues_count,
                'total_issues': open_issues_count + closed_issues_count
            }
        except:
            return {
                'open_issues': 0,
                'closed_issues': 0,
                'total_issues': 0
            }
    
    def get_pull_requests(self, owner: str, repo: str, state: str = 'open') -> List[Dict]:
        url = f"{self.base_url}/repos/{owner}/{repo}/pulls?state={state}&per_page=10"
        try:
            prs_data = self._make_request(url)
            
            prs = []
            for pr in prs_data:
                prs.append({
                    'number': pr['number'],
                    'title': pr['title'],
                    'user': pr['user']['login'],
                    'created_at': pr['created_at'],
                    'updated_at': pr['updated_at'],
                    'url': pr['html_url']
                })
            
            return prs
        except:
            return []