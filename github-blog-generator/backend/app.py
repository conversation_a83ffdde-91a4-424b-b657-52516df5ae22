from flask import Flask, render_template, request, jsonify
import os
from dotenv import load_dotenv
from github_api import GitHubAPI
from content_generator import ContentGenerator

load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'dev-secret-key')

github_api = GitHubAPI(os.getenv('GITHUB_TOKEN'))
content_generator = ContentGenerator()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/analyze', methods=['POST'])
def analyze_repository():
    data = request.get_json()
    repo_url = data.get('repo_url')
    
    if not repo_url:
        return jsonify({'error': 'Repository URL is required'}), 400
    
    try:
        # Extract owner and repo name from URL
        parts = repo_url.strip('/').split('/')
        if len(parts) < 2:
            return jsonify({'error': 'Invalid repository URL'}), 400
        
        owner = parts[-2]
        repo_name = parts[-1]
        
        # Fetch repository data
        repo_data = github_api.get_repository_data(owner, repo_name)
        
        # Generate content
        blog_content = content_generator.generate_blog_post(repo_data)
        
        return jsonify({
            'success': True,
            'data': {
                'repository': repo_data,
                'blog_content': blog_content
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/repositories/<owner>/<repo>')
def get_repository(owner, repo):
    try:
        repo_data = github_api.get_repository_data(owner, repo)
        return jsonify({'success': True, 'data': repo_data})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-blog', methods=['POST'])
def generate_blog():
    data = request.get_json()
    repo_data = data.get('repo_data')
    
    if not repo_data:
        return jsonify({'error': 'Repository data is required'}), 400
    
    try:
        blog_content = content_generator.generate_blog_post(repo_data)
        return jsonify({'success': True, 'content': blog_content})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)