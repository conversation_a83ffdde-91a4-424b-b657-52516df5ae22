from flask import Flask, render_template, request, jsonify, redirect, url_for
import os
import uuid
import json
from datetime import datetime
from dotenv import load_dotenv
from github_api import GitHubAPI
from content_generator import ContentGenerator

load_dotenv()

app = Flask(__name__, template_folder='../templates', static_folder='../static')
app.secret_key = os.getenv('SECRET_KEY', 'dev-secret-key')

github_api = GitHubAPI(os.getenv('GITHUB_TOKEN'))
content_generator = ContentGenerator()

# 内存存储生成的博客（生产环境应使用数据库）
blog_storage = {}

@app.route('/')
def index():
    # 渲染前端主页
    try:
        import os
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend', 'index.html')
        with open(frontend_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"Error loading frontend: {e}")
        return render_template('error.html', 
                             error_message='前端页面未找到',
                             error_code=500), 500

@app.route('/frontend/<path:filename>')
def frontend_static(filename):
    """为前端文件提供静态文件服务"""
    from flask import send_from_directory
    import os
    frontend_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend')
    return send_from_directory(frontend_dir, filename)

@app.route('/api/generate-blog', methods=['POST'])
def generate_blog():
    """生成博客并返回博客页面URL"""
    data = request.get_json()
    github_url = data.get('github_url')
    custom_title = data.get('custom_title', '')
    
    if not github_url:
        return jsonify({'error': 'GitHub URL is required'}), 400
    
    try:
        # 验证和解析GitHub URL
        if 'github.com' not in github_url:
            return jsonify({'error': 'Invalid GitHub URL'}), 400
            
        # 提取owner和repo
        parts = github_url.strip('/').split('/')
        if len(parts) < 2:
            return jsonify({'error': 'Invalid repository URL format'}), 400

        owner = parts[-2]
        repo_name = parts[-1]

        # 移除.git后缀（如果存在）
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
        
        # 获取仓库数据
        repo_data = github_api.get_repository_data(owner, repo_name)
        
        # 生成博客内容
        blog_content = content_generator.generate_blog_post(repo_data)
        
        # 生成唯一的博客ID
        blog_id = str(uuid.uuid4())[:8]
        
        # 准备博客数据
        blog_data = {
            'id': blog_id,
            'title': custom_title or f"{repo_name} - GitHub项目分析",
            'subtitle': f"深入分析 {owner}/{repo_name} 项目的技术实现",
            'github_url': github_url,
            'project_owner': owner,
            'project_name': repo_name,
            'author': owner,
            'date_published': datetime.now().isoformat(),
            'date_formatted': datetime.now().strftime('%Y年%m月%d日'),
            'repository_data': repo_data,
            'blog_content': blog_content,
            'generated_at': datetime.now().isoformat()
        }
        
        # 存储博客数据
        blog_storage[blog_id] = blog_data
        
        # 返回博客URL
        blog_url = f"/blog/{blog_id}"
        
        return jsonify({
            'success': True,
            'blog_url': blog_url,
            'blog_id': blog_id,
            'message': '博客生成成功！'
        })
        
    except Exception as e:
        print(f"Error generating blog: {e}")
        return jsonify({'error': f'生成博客时出错: {str(e)}'}), 500

@app.route('/blog/<blog_id>')
def show_blog(blog_id):
    """显示生成的博客页面"""
    # 从存储中获取博客数据
    blog_data = blog_storage.get(blog_id)
    
    if not blog_data:
        return render_template('error.html', 
                             error_message='博客不存在或已过期',
                             error_code=404), 404
    
    # 准备渲染数据
    repo_data = blog_data['repository_data']
    basic_info = repo_data['basic_info']
    
    # 处理项目统计数据
    project_stats = [
        {'value': f"{basic_info['stars']:,}", 'label': 'Stars'},
        {'value': f"{basic_info['forks']:,}", 'label': 'Forks'},
        {'value': f"{basic_info['watchers']:,}", 'label': 'Watchers'},
        {'value': f"{basic_info['open_issues']:,}", 'label': 'Issues'}
    ]
    
    # 处理技术栈数据
    tech_stack = []
    if repo_data.get('languages'):
        total_bytes = sum(repo_data['languages'].values())
        for lang, bytes_count in sorted(repo_data['languages'].items(), key=lambda x: x[1], reverse=True):
            percentage = (bytes_count / total_bytes) * 100
            tech_stack.append({
                'category': lang,
                'description': f"{percentage:.1f}% 的代码库",
                'technologies': [lang],
                'icon': 'fas fa-code'
            })
    
    # 处理核心特性
    features = [
        {
            'title': '代码质量',
            'description': f"主要使用 {basic_info['language']} 开发，代码结构清晰",
            'icon': 'fas fa-code-branch',
            'benefits': ['结构化代码', '可维护性强', '团队协作友好']
        },
        {
            'title': '社区活跃度',
            'description': f"拥有 {basic_info['stars']} 个 Star 和 {len(repo_data.get('contributors', []))} 位贡献者",
            'icon': 'fas fa-users',
            'benefits': ['活跃的开发者社区', '持续的功能更新', '问题快速响应']
        },
        {
            'title': '项目稳定性',
            'description': f"已有 {basic_info['forks']} 个 Fork，项目稳定可靠",
            'icon': 'fas fa-shield-alt',
            'benefits': ['经过验证的代码', '稳定的API', '向后兼容']
        }
    ]
    
    # 处理代码洞察
    code_insights = []
    if repo_data.get('commits'):
        latest_commit = repo_data['commits'][0]
        code_insights.append({
            'title': '最新提交分析',
            'description': f"最新提交: {latest_commit['message']}",
            'highlights': [
                f"提交者: {latest_commit['author']}",
                f"提交时间: {latest_commit['date']}",
                f"提交SHA: {latest_commit['sha']}"
            ],
            'file_name': '最新提交信息',
            'language': 'text'
        })
    
    # 渲染模板数据
    template_data = {
        # 基本信息
        'title': blog_data['title'],
        'subtitle': blog_data['subtitle'],
        'github_url': blog_data['github_url'],
        'project_owner': blog_data['project_owner'],
        'project_name': blog_data['project_name'],
        'author': blog_data['author'],
        'date_published': blog_data['date_published'],
        'date_formatted': blog_data['date_formatted'],
        
        # 项目信息
        'project_description': basic_info.get('description', '暂无描述'),
        'project_language': basic_info.get('language', 'Unknown'),
        'project_stars': basic_info['stars'],
        'project_forks': basic_info['forks'],
        'project_license': basic_info.get('license', 'No license'),
        'project_stats': project_stats,
        
        # 内容数据
        'tech_stack': tech_stack,
        'features': features,
        'code_insights': code_insights,
        
        # 其他
        'reading_time': '5-8',
        'conclusion': f"{blog_data['project_name']} 是一个优秀的开源项目，展现了现代软件开发的最佳实践。通过深入分析，我们可以看到该项目在技术选型、代码组织和社区建设方面都表现出色。",
        'key_takeaways': [
            f"技术栈合理，主要使用 {basic_info['language']}",
            f"社区活跃，拥有 {basic_info['stars']} 个 Star",
            f"代码质量高，有 {len(repo_data.get('contributors', []))} 位贡献者",
            "项目文档完善，易于理解和使用"
        ]
    }
    
    return render_template('blog.html', **template_data)

@app.route('/api/repositories/<owner>/<repo>')
def get_repository(owner, repo):
    """获取仓库信息API"""
    try:
        repo_data = github_api.get_repository_data(owner, repo)
        return jsonify({'success': True, 'data': repo_data})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)