#!/usr/bin/env python3
"""
完整功能测试脚本
测试GitHub博客生成器的完整工作流程
"""

import requests
import json
import time
import sys

def test_api_endpoint():
    """测试API端点"""
    print("🧪 测试API端点...")
    
    # 测试数据
    test_repos = [
        "https://github.com/torvalds/linux",
        "https://github.com/microsoft/vscode",
        "https://github.com/facebook/react"
    ]
    
    for repo_url in test_repos:
        print(f"\n📝 测试仓库: {repo_url}")
        
        try:
            # 发送POST请求
            response = requests.post(
                'http://localhost:5000/api/generate-blog',
                json={'github_url': repo_url},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                blog_url = result.get('blog_url', '')
                print(f"✅ 博客生成成功: {blog_url}")
                
                # 测试博客页面是否可访问
                if blog_url:
                    blog_response = requests.get(f"http://localhost:5000{blog_url}")
                    if blog_response.status_code == 200:
                        print(f"✅ 博客页面可访问")
                        return True
                    else:
                        print(f"❌ 博客页面不可访问: {blog_response.status_code}")
                        return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return False
    
    return True

def test_frontend_access():
    """测试前端页面访问"""
    print("\n🌐 测试前端页面...")
    
    try:
        response = requests.get('http://localhost:5000/')
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            return True
        else:
            print(f"❌ 前端页面不可访问: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端访问异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始完整功能测试...")
    print("=" * 50)
    
    # 测试前端访问
    if not test_frontend_access():
        print("\n❌ 前端测试失败，请检查服务器是否启动")
        sys.exit(1)
    
    # 测试API功能
    if not test_api_endpoint():
        print("\n❌ API测试失败")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！GitHub博客生成器工作正常！")
    print("\n📍 访问地址:")
    print("   前端: http://localhost:5000")
    print("   API:  http://localhost:5000/api/generate-blog")
    print("\n💡 使用方法:")
    print("   1. 在浏览器中打开 http://localhost:5000")
    print("   2. 输入GitHub仓库URL")
    print("   3. 点击'生成博客'按钮")
    print("   4. 等待跳转到生成的博客页面")

if __name__ == "__main__":
    main()
