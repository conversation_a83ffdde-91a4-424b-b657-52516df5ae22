#!/usr/bin/env python3
"""
最简化的测试服务器
"""

try:
    from flask import Flask
    
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>GitHub博客生成器</title>
            <style>
                body { font-family: Arial; margin: 50px; }
                .container { max-width: 600px; margin: 0 auto; }
                input { padding: 10px; width: 300px; margin: 10px; }
                button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 GitHub博客生成器</h1>
                <p>✅ 服务器正在运行！</p>
                <p>📍 地址: http://localhost:5000</p>
                
                <h2>测试功能</h2>
                <form action="/generate" method="post">
                    <input type="text" name="github_url" placeholder="输入GitHub仓库URL" required>
                    <br>
                    <button type="submit">生成博客</button>
                </form>
            </div>
        </body>
        </html>
        '''
    
    @app.route('/generate', methods=['POST'])
    def generate():
        from flask import request
        github_url = request.form.get('github_url', '')
        
        return f'''
        <!DOCTYPE html>
        <html>
        <head>
            <title>生成结果</title>
            <style>
                body {{ font-family: Arial; margin: 50px; }}
                .container {{ max-width: 600px; margin: 0 auto; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎉 测试成功！</h1>
                <p>✅ 服务器正常工作</p>
                <p>📝 接收到的URL: {github_url}</p>
                <p>🔗 <a href="/">返回主页</a></p>
                
                <h2>下一步</h2>
                <p>基础功能正常，现在可以启用完整版本</p>
            </div>
        </body>
        </html>
        '''
    
    if __name__ == '__main__':
        print("启动最简化服务器...")
        app.run(host='0.0.0.0', port=5000, debug=False)
        
except ImportError:
    print("Flask未安装，请运行: pip install flask")
except Exception as e:
    print(f"启动失败: {e}")
