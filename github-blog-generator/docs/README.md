# GitHub 博客生成器

## 📖 项目简介

GitHub 博客生成器是一个强大的工具，能够自动分析 GitHub 项目并生成精美的技术博客。它采用现代化的前后端分离架构，提供直观的用户界面和智能的内容生成功能。

### ✨ 主要特性

- 🚀 **智能分析**: 自动分析 GitHub 项目结构、代码特点和技术栈
- 🎨 **美观设计**: 现代化响应式设计，支持多种主题风格
- 💻 **代码高亮**: 智能代码语法高亮和项目结构可视化
- 📱 **响应式**: 完美适配桌面端、平板和移动设备
- 🌙 **主题切换**: 支持深色/浅色主题切换
- 📋 **一键复制**: 代码块一键复制功能
- 🔗 **分享功能**: 支持多平台分享
- 🖨️ **打印优化**: 针对打印进行优化
- ♿ **无障碍**: 遵循无障碍设计标准

## 🏗️ 技术架构

### 后端技术栈
- **Python 3.7+**: 主要编程语言
- **Flask**: Web 框架
- **Requests**: HTTP 客户端
- **Jinja2**: 模板引擎
- **GitHub API**: 项目数据获取

### 前端技术栈
- **HTML5**: 语义化标记
- **CSS3**: 现代化样式（Grid、Flexbox、动画）
- **JavaScript ES6+**: 交互功能
- **Font Awesome**: 图标库
- **Google Fonts**: 字体资源

## 📂 项目结构

```
github-blog-generator/
├── backend/                    # 后端代码
│   ├── app.py                 # Flask 主应用
│   ├── github_api.py          # GitHub API 接口
│   └── content_generator.py   # 内容生成器
├── frontend/                   # 前端代码
│   ├── index.html            # 主页面
│   ├── style.css             # 主样式文件
│   └── script.js             # 主JavaScript文件
├── static/                     # 静态资源
│   ├── css/
│   │   └── blog.css          # 博客样式
│   └── js/
│       └── blog.js           # 博客功能
├── templates/                  # 模板文件
│   ├── base.html             # 基础模板
│   └── blog.html             # 博客模板
├── docs/                       # 文档目录
│   └── README.md             # 项目文档
├── run.py                      # 启动脚本
├── requirements.txt            # Python依赖
└── README.md                   # 项目说明
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.7 或更高版本
- pip 包管理器
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 安装依赖

```bash
# 克隆项目（如果适用）
git clone <项目地址>
cd github-blog-generator

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 启动服务

#### 方式一：一键启动（推荐）
```bash
python run.py
```

#### 方式二：分别启动
```bash
# 启动后端服务
python run.py --backend

# 在新终端启动前端服务
python run.py --frontend
```

### 4. 访问应用

- 前端界面: http://localhost:8080
- 后端API: http://localhost:5000

## 💡 使用指南

### 基本使用流程

1. **打开应用**: 在浏览器中访问 http://localhost:8080
2. **输入GitHub URL**: 在输入框中粘贴GitHub项目链接
3. **自定义标题**: 可选择性地自定义博客标题
4. **生成博客**: 点击"生成博客"按钮
5. **预览和下载**: 查看生成的博客并可下载HTML文件

### GitHub URL 格式

支持的 GitHub URL 格式：
```
https://github.com/username/repository
https://github.com/username/repository/
```

### 功能说明

#### 🎨 主题切换
- 点击页面右上角的月亮/太阳图标切换主题
- 设置会自动保存到本地存储

#### 📋 代码复制
- 悬停到代码块右上角，点击复制图标
- 代码会自动复制到剪贴板

#### 🔗 分享功能
- 点击分享按钮可分享到社交媒体
- 支持 Twitter、Facebook、LinkedIn
- 或直接复制链接

#### 🖨️ 打印博客
- 点击打印按钮或使用 Ctrl+P
- 样式已针对打印进行优化

## ⚙️ 配置选项

### 环境变量

可以通过环境变量配置以下选项：

```bash
# GitHub API Token（提高API限制）
export GITHUB_TOKEN=your_github_token

# 开发模式
export FLASK_ENV=development
export FLASK_DEBUG=1

# 端口配置
export BACKEND_PORT=5000
export FRONTEND_PORT=8080
```

### 自定义配置

可以在 `backend/app.py` 中修改以下配置：

```python
# API配置
GITHUB_API_BASE = "https://api.github.com"
REQUEST_TIMEOUT = 30

# 内容生成配置
MAX_CONTENT_LENGTH = 10000
ENABLE_CODE_ANALYSIS = True
```

## 🔧 开发指南

### 后端开发

后端采用 Flask 框架，主要模块：

- `app.py`: 主应用和路由定义
- `github_api.py`: GitHub API 交互
- `content_generator.py`: 博客内容生成

#### 添加新的API端点

```python
@app.route('/api/new-endpoint', methods=['POST'])
def new_endpoint():
    data = request.get_json()
    # 处理逻辑
    return jsonify(result)
```

### 前端开发

前端使用原生 JavaScript，主要文件：

- `frontend/index.html`: 主页面结构
- `frontend/style.css`: 样式定义
- `frontend/script.js`: 交互逻辑

#### 添加新功能

```javascript
class NewFeature {
    constructor() {
        this.init();
    }
    
    init() {
        // 初始化逻辑
    }
}

// 页面加载后初始化
document.addEventListener('DOMContentLoaded', () => {
    new NewFeature();
});
```

## 🐛 常见问题

### Q: 服务启动失败
A: 检查端口是否被占用，确保 Python 和依赖正确安装

### Q: GitHub API 限制
A: 设置 GITHUB_TOKEN 环境变量提高 API 限制

### Q: 生成的博客样式异常
A: 确保静态文件路径正确，检查浏览器控制台错误

### Q: 中文内容显示异常
A: 确保文件编码为 UTF-8

## 🔒 安全注意事项

- 不要在生产环境中启用 Flask 调试模式
- 妥善保管 GitHub Token，不要提交到代码仓库
- 定期更新依赖包，修复安全漏洞
- 对用户输入进行适当验证和过滤

## 📚 API 文档

### 生成博客

**POST** `/api/generate-blog`

请求体：
```json
{
    "github_url": "https://github.com/username/repo",
    "custom_title": "自定义标题（可选）"
}
```

响应：
```json
{
    "success": true,
    "data": {
        "title": "博客标题",
        "content": "HTML内容",
        "metadata": {
            "author": "作者",
            "date": "日期",
            "stats": {}
        }
    }
}
```

### 获取项目信息

**GET** `/api/project-info?url=<github_url>`

响应：
```json
{
    "success": true,
    "data": {
        "name": "项目名称",
        "description": "项目描述",
        "language": "主要语言",
        "stars": 123,
        "forks": 45
    }
}
```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范

- Python: 遵循 PEP 8 标准
- JavaScript: 使用 ES6+ 语法
- CSS: 使用 BEM 命名规范
- 提交信息: 使用简洁明确的描述

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 支持与联系

- 📧 邮箱: <EMAIL>
- 📱 电话: +86 400-123-4567
- 💬 QQ群: 123456789
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

## 🙏 致谢

感谢以下开源项目和贡献者：

- [Flask](https://flask.palletsprojects.com/) - Web 框架
- [Font Awesome](https://fontawesome.com/) - 图标库
- [Highlight.js](https://highlightjs.org/) - 代码高亮
- [Google Fonts](https://fonts.google.com/) - 字体资源

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！