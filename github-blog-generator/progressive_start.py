#!/usr/bin/env python3
"""
渐进式启动脚本 - 逐步启用功能
"""

import sys
import os

# 添加backend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """测试导入"""
    try:
        print("测试Flask导入...")
        from flask import Flask
        print("✅ Flask导入成功")
        
        print("测试其他依赖...")
        import requests
        print("✅ requests导入成功")
        
        import matplotlib
        matplotlib.use('Agg')  # 非交互式后端
        print("✅ matplotlib导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def start_full_server():
    """启动完整服务器"""
    try:
        print("启动完整版GitHub博客生成器...")
        
        # 导入完整应用
        from backend.app import app
        
        print("✅ 完整应用加载成功")
        print("📍 访问地址: http://localhost:5000")
        
        # 启动服务器
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False
        )
        
    except Exception as e:
        print(f"❌ 完整版启动失败: {e}")
        print("💡 回退到简化版本...")
        
        # 回退到简化版本
        from flask import Flask, request
        
        app = Flask(__name__)
        
        @app.route('/')
        def home():
            return '''
            <h1>🚀 GitHub博客生成器 (简化版)</h1>
            <p>✅ 基础服务正常</p>
            <p>⚠️ 完整功能暂时不可用</p>
            <p>📍 地址: http://localhost:5000</p>
            '''
        
        app.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == '__main__':
    print("🔍 渐进式启动GitHub博客生成器...")
    print("=" * 50)
    
    # 切换到项目目录
    os.chdir(os.path.dirname(__file__))
    
    # 测试导入
    if test_imports():
        print("✅ 所有依赖正常，启动完整版本...")
        start_full_server()
    else:
        print("❌ 依赖有问题，请安装: pip install flask requests matplotlib seaborn pandas")
