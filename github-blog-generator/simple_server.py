#!/usr/bin/env python3
"""
简化版服务器 - 用于测试基本功能
"""

from flask import Flask, render_template, request, jsonify
import os

# 创建Flask应用
app = Flask(__name__, 
           template_folder='templates',
           static_folder='static')

@app.route('/')
def index():
    """主页"""
    try:
        return render_template('index.html')
    except Exception as e:
        return f"""
        <html>
        <head><title>GitHub博客生成器</title></head>
        <body>
            <h1>🚀 GitHub博客生成器</h1>
            <p>✅ 服务器正在运行！</p>
            <p>📍 端口: 5000</p>
            <p>⚠️ 模板加载错误: {str(e)}</p>
            <form action="/test" method="post">
                <input type="text" name="github_url" placeholder="输入GitHub URL" style="width:300px;padding:10px;">
                <button type="submit" style="padding:10px;">测试</button>
            </form>
        </body>
        </html>
        """

@app.route('/test', methods=['POST'])
def test():
    """测试功能"""
    github_url = request.form.get('github_url', '')
    return f"""
    <html>
    <head><title>测试结果</title></head>
    <body>
        <h1>🧪 测试结果</h1>
        <p>✅ 服务器正常工作</p>
        <p>📝 接收到的URL: {github_url}</p>
        <p>🔗 <a href="/">返回主页</a></p>
    </body>
    </html>
    """

@app.route('/status')
def status():
    """状态检查"""
    return jsonify({
        'status': 'running',
        'port': 5000,
        'message': '服务器正常运行'
    })

if __name__ == '__main__':
    print("🚀 启动简化版GitHub博客生成器...")
    print("📍 访问地址: http://localhost:5000")
    print("📍 状态检查: http://localhost:5000/status")
    
    # 启动服务器
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False
    )
