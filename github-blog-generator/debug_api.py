#!/usr/bin/env python3
"""
调试API问题
"""

import sys
import os
import requests
import json
import time

# 添加backend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_api_directly():
    """直接测试API功能"""
    print("🔍 直接测试后端API功能...")
    
    try:
        from github_api import GitHubAPI
        from content_generator import ContentGenerator
        
        # 初始化组件
        github_api = GitHubAPI()
        content_generator = ContentGenerator()
        
        print("✅ 组件初始化成功")
        
        # 测试GitHub API
        print("📡 测试GitHub API...")
        repo_data = github_api.get_repository_data('microsoft', 'vscode')
        print(f"✅ GitHub API调用成功，获取到数据: {len(str(repo_data))} 字符")
        
        # 测试内容生成
        print("📝 测试内容生成...")
        blog_content = content_generator.generate_blog_post(repo_data)
        print(f"✅ 内容生成成功，生成内容: {len(str(blog_content))} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        # 测试主页
        response = requests.get('http://localhost:5000/', timeout=5)
        print(f"主页状态码: {response.status_code}")
        
        # 测试API端点
        api_data = {
            'github_url': 'https://github.com/microsoft/vscode',
            'custom_title': '测试博客'
        }
        
        print("📤 发送API请求...")
        response = requests.post(
            'http://localhost:5000/api/generate-blog',
            json=api_data,
            timeout=60
        )
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API调用成功: {result}")
            
            # 测试博客页面
            if 'blog_url' in result:
                blog_response = requests.get(f"http://localhost:5000{result['blog_url']}", timeout=10)
                print(f"博客页面状态码: {blog_response.status_code}")
                
                if blog_response.status_code == 200:
                    print("✅ 博客页面渲染成功")
                    return True
                else:
                    print(f"❌ 博客页面渲染失败: {blog_response.text[:200]}")
            
        else:
            print(f"❌ API调用失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务正在运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        
    return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    
    try:
        response = requests.get('http://localhost:5000/', timeout=3)
        if response.status_code == 200:
            print("✅ 服务器正在运行")
            return True
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
            return False
    except:
        print("❌ 服务器未运行或无法连接")
        return False

def main():
    """主函数"""
    print("🚀 开始调试GitHub博客生成器API...")
    print("=" * 60)
    
    # 检查服务器状态
    server_running = check_server_status()
    
    # 直接测试后端功能
    print("\n📋 测试1: 直接测试后端功能")
    direct_test_ok = test_api_directly()
    
    # 测试API端点
    if server_running:
        print("\n📋 测试2: 测试API端点")
        api_test_ok = test_api_endpoint()
    else:
        print("\n⚠️ 跳过API端点测试（服务器未运行）")
        api_test_ok = False
    
    print("\n" + "=" * 60)
    print("🎯 调试结果:")
    print(f"  直接后端测试: {'✅ 通过' if direct_test_ok else '❌ 失败'}")
    print(f"  API端点测试: {'✅ 通过' if api_test_ok else '❌ 失败'}")
    
    if not direct_test_ok:
        print("\n💡 建议:")
        print("  1. 检查GitHub API配置")
        print("  2. 检查content_generator.py中的方法实现")
        print("  3. 检查依赖包是否正确安装")
    
    if not api_test_ok and server_running:
        print("\n💡 建议:")
        print("  1. 检查Flask路由配置")
        print("  2. 查看服务器日志输出")
        print("  3. 检查博客模板渲染")

if __name__ == "__main__":
    main()
