{% extends "base.html" %}

{% block extra_css %}
<style>
    /* Blog-specific styles */
    .blog-header-hero {
        background: linear-gradient(135deg, {{ project_color | default('#667eea') }} 0%, {{ project_color_secondary | default('#764ba2') }} 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .blog-header-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="3" fill="rgba(255,255,255,0.05)"/></svg>');
        animation: float 20s infinite linear;
    }
    
    @keyframes float {
        from { transform: translateY(0) rotate(0deg); }
        to { transform: translateY(-100px) rotate(360deg); }
    }
    
    .hero-content {
        position: relative;
        z-index: 1;
        max-width: 800px;
        margin: 0 auto;
        padding: 0 2rem;
    }
    
    .project-badges {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    .badge {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-size: 0.875rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .reading-time {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        margin-top: 1rem;
    }
    
    .blog-navigation {
        background: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 0;
        z-index: 100;
        margin-bottom: 2rem;
    }

    /* 文章容器 - 居中布局，限制宽度 */
    .article-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
        line-height: 1.7;
        font-size: 16px;
        color: #2d3748;
    }

    /* Markdown风格标题层次 */
    .article-container h1 {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1a202c;
        margin: 2rem 0 1.5rem 0;
        line-height: 1.2;
        border-bottom: 3px solid #667eea;
        padding-bottom: 0.5rem;
    }

    .article-container h2 {
        font-size: 2rem;
        font-weight: 600;
        color: #2d3748;
        margin: 2.5rem 0 1rem 0;
        line-height: 1.3;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 0.5rem;
    }

    .article-container h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #4a5568;
        margin: 2rem 0 1rem 0;
        line-height: 1.4;
    }

    .article-container h4 {
        font-size: 1.25rem;
        font-weight: 500;
        color: #718096;
        margin: 1.5rem 0 0.75rem 0;
        line-height: 1.4;
    }

    /* 段落样式 */
    .article-container p {
        margin: 1rem 0;
        line-height: 1.7;
    }

    /* 列表样式 */
    .article-container ul, .article-container ol {
        margin: 1rem 0;
        padding-left: 2rem;
    }

    .article-container li {
        margin: 0.5rem 0;
        line-height: 1.6;
    }
    
    .nav-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1rem 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .nav-toc {
        display: flex;
        gap: 2rem;
    }
    
    .nav-toc a {
        color: #64748b;
        text-decoration: none;
        font-weight: 500;
        padding: 0.5rem 0;
        border-bottom: 2px solid transparent;
        transition: all 0.2s ease;
    }
    
    .nav-toc a:hover,
    .nav-toc a.active {
        color: {{ project_color | default('#667eea') }};
        border-bottom-color: {{ project_color | default('#667eea') }};
    }
    
    .blog-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }
    
    .action-button {
        padding: 0.5rem 1rem;
        border: 1px solid #e2e8f0;
        background: white;
        color: #64748b;
        border-radius: 0.5rem;
        text-decoration: none;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .action-button:hover {
        border-color: {{ project_color | default('#667eea') }};
        color: {{ project_color | default('#667eea') }};
    }
    
    .action-button.primary {
        background: {{ project_color | default('#667eea') }};
        color: white;
        border-color: {{ project_color | default('#667eea') }};
    }
    
    .action-button.primary:hover {
        background: {{ project_color_secondary | default('#764ba2') }};
        border-color: {{ project_color_secondary | default('#764ba2') }};
    }
</style>
{% endblock %}

{% block content %}
<!-- Blog Hero Section -->
<section class="blog-header-hero">
    <div class="hero-content">
        <div class="project-badges">
            {% if project_language %}
            <span class="badge">
                <i class="fas fa-code"></i>
                {{ project_language }}
            </span>
            {% endif %}
            
            {% if project_stars %}
            <span class="badge">
                <i class="fas fa-star"></i>
                {{ project_stars }} Stars
            </span>
            {% endif %}
            
            {% if project_forks %}
            <span class="badge">
                <i class="fas fa-code-branch"></i>
                {{ project_forks }} Forks
            </span>
            {% endif %}
            
            {% if project_license %}
            <span class="badge">
                <i class="fas fa-balance-scale"></i>
                {{ project_license }}
            </span>
            {% endif %}
        </div>
        
        <h1 class="blog-title">{{ title }}</h1>
        <p class="blog-subtitle">{{ subtitle }}</p>
        
        <div class="blog-meta">
            <div class="meta-item">
                <i class="fab fa-github"></i>
                <a href="{{ github_url }}" target="_blank" rel="noopener noreferrer">
                    {{ project_owner }}/{{ project_name }}
                </a>
            </div>
            
            <div class="meta-item">
                <i class="fas fa-calendar"></i>
                <time datetime="{{ date_published }}">{{ date_formatted }}</time>
            </div>
            
            <div class="meta-item">
                <i class="fas fa-user"></i>
                <span>{{ author }}</span>
            </div>
            
            {% if reading_time %}
            <div class="meta-item reading-time">
                <i class="fas fa-clock"></i>
                <span>{{ reading_time }} 分钟阅读</span>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Blog Navigation -->
<nav class="blog-navigation">
    <div class="nav-content">
        <div class="nav-toc">
            <a href="#overview">项目概述</a>
            <a href="#architecture">技术架构</a>
            <a href="#features">核心特性</a>
            <a href="#code-analysis">代码分析</a>
            <a href="#conclusion">总结</a>
        </div>
        
        <div class="blog-actions">
            <a href="{{ github_url }}" class="action-button primary" target="_blank" rel="noopener noreferrer">
                <i class="fab fa-github"></i>
                查看源码
            </a>
            <button class="action-button" onclick="window.print()">
                <i class="fas fa-print"></i>
                打印
            </button>
            <button class="action-button" onclick="shareArticle()">
                <i class="fas fa-share"></i>
                分享
            </button>
        </div>
    </div>
</nav>

<!-- Main Blog Content -->
<article class="blog-content">
    <div class="article-container">
    <!-- Project Overview Section -->
    <section id="overview" class="section">
        <h2 class="section-title">
            <i class="fas fa-info-circle"></i>
            项目概述
        </h2>
        
        <div class="section-content">
            <div class="project-summary">
                <div class="summary-text">
                    <p>{{ project_description | default('这是一个优秀的开源项目，展示了现代软件开发的最佳实践。') }}</p>
                    
                    {% if project_topics %}
                    <div class="project-topics">
                        <h4>相关主题：</h4>
                        <div class="topics-list">
                            {% for topic in project_topics %}
                            <span class="topic-tag">{{ topic }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                {% if project_screenshot %}
                <div class="summary-image">
                    <img src="{{ project_screenshot }}" alt="{{ title }} 项目截图" class="project-screenshot">
                </div>
                {% endif %}
            </div>
            
            <!-- Project Statistics -->
            <div class="stats-grid">
                {% if project_stats %}
                {% for stat in project_stats %}
                <div class="stat-item">
                    <span class="stat-number">{{ stat.value }}</span>
                    <span class="stat-label">{{ stat.label }}</span>
                </div>
                {% endfor %}
                {% endif %}
            </div>
        </div>
    </section>
    
    <!-- Technical Architecture Section -->
    <section id="architecture" class="section">
        <h2 class="section-title">
            <i class="fas fa-sitemap"></i>
            技术架构
        </h2>
        
        <div class="section-content">
            {% if tech_stack %}
            <div class="tech-grid">
                {% for tech in tech_stack %}
                <div class="tech-item">
                    <div class="tech-icon">
                        {% if tech.icon %}
                        <i class="{{ tech.icon }}"></i>
                        {% else %}
                        <i class="fas fa-cube"></i>
                        {% endif %}
                    </div>
                    <h4>{{ tech.category }}</h4>
                    <p>{{ tech.description }}</p>
                    {% if tech.technologies %}
                    <div class="tech-list">
                        {% for technology in tech.technologies %}
                        <span class="tech-badge">{{ technology }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if architecture_diagram %}
            <div class="architecture-diagram">
                <h4>系统架构图</h4>
                <img src="{{ architecture_diagram }}" alt="系统架构图" class="diagram-image">
            </div>
            {% endif %}
        </div>
    </section>
    
    <!-- Core Features Section -->
    <section id="features" class="section">
        <h2 class="section-title">
            <i class="fas fa-star"></i>
            核心特性
        </h2>
        
        <div class="section-content">
            {% if features %}
            <div class="features-showcase">
                {% for feature in features %}
                <div class="feature-item">
                    <div class="feature-header">
                        <div class="feature-icon">
                            {% if feature.icon %}
                            <i class="{{ feature.icon }}"></i>
                            {% else %}
                            <i class="fas fa-cog"></i>
                            {% endif %}
                        </div>
                        <h3>{{ feature.title }}</h3>
                    </div>
                    <div class="feature-content">
                        <p>{{ feature.description }}</p>
                        {% if feature.benefits %}
                        <ul class="feature-benefits">
                            {% for benefit in feature.benefits %}
                            <li>{{ benefit }}</li>
                            {% endfor %}
                        </ul>
                        {% endif %}
                        {% if feature.code_example %}
                        <div class="feature-code">
                            <h5>代码示例：</h5>
                            <pre><code class="{{ feature.language | default('javascript') }}">{{ feature.code_example }}</code></pre>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </section>
    
    <!-- Code Analysis Section -->
    <section id="code-analysis" class="section">
        <h2 class="section-title">
            <i class="fas fa-code"></i>
            代码分析
        </h2>
        
        <div class="section-content">
            {% if code_insights %}
            <div class="code-insights">
                {% for insight in code_insights %}
                <div class="insight-item">
                    <h4>{{ insight.title }}</h4>
                    <div class="insight-content">
                        <div class="insight-description">
                            <p>{{ insight.description }}</p>
                            {% if insight.highlights %}
                            <ul class="insight-highlights">
                                {% for highlight in insight.highlights %}
                                <li>{{ highlight }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                        
                        {% if insight.code %}
                        <div class="code-block">
                            <div class="code-header">
                                <span class="file-name">
                                    <i class="fas fa-file-code"></i>
                                    {{ insight.file_name | default('示例代码') }}
                                </span>
                                <button class="copy-code-btn" onclick="copyCode(this)">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <pre><code class="{{ insight.language | default('javascript') }}">{{ insight.code }}</code></pre>
                        </div>
                        {% endif %}
                        
                        {% if insight.explanation %}
                        <div class="code-explanation">
                            <h5>代码说明：</h5>
                            <p>{{ insight.explanation }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- File Structure -->
            {% if file_structure %}
            <div class="file-structure">
                <h4>项目结构</h4>
                <div class="file-tree">
                    {{ file_structure | safe }}
                </div>
            </div>
            {% endif %}
        </div>
    </section>
    
    <!-- Installation & Usage -->
    {% if installation_guide or usage_examples %}
    <section id="usage" class="section">
        <h2 class="section-title">
            <i class="fas fa-play"></i>
            安装与使用
        </h2>
        
        <div class="section-content">
            {% if installation_guide %}
            <div class="installation-guide">
                <h4>安装步骤</h4>
                <div class="install-steps">
                    {% for step in installation_guide %}
                    <div class="install-step">
                        <div class="step-number">{{ loop.index }}</div>
                        <div class="step-content">
                            <h5>{{ step.title }}</h5>
                            <p>{{ step.description }}</p>
                            {% if step.command %}
                            <pre><code class="bash">{{ step.command }}</code></pre>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            {% if usage_examples %}
            <div class="usage-examples">
                <h4>使用示例</h4>
                {% for example in usage_examples %}
                <div class="usage-example">
                    <h5>{{ example.title }}</h5>
                    <p>{{ example.description }}</p>
                    <pre><code class="{{ example.language | default('javascript') }}">{{ example.code }}</code></pre>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </section>
    {% endif %}
    
    <!-- Conclusion Section -->
    <section id="conclusion" class="section">
        <h2 class="section-title">
            <i class="fas fa-flag-checkered"></i>
            总结与展望
        </h2>
        
        <div class="section-content">
            <div class="conclusion-content">
                <p>{{ conclusion | default('通过对这个项目的深入分析，我们可以看到它展现了优秀的软件工程实践和创新的技术应用。') }}</p>
                
                {% if key_takeaways %}
                <div class="key-takeaways">
                    <h4>关键要点：</h4>
                    <ul class="takeaways-list">
                        {% for takeaway in key_takeaways %}
                        <li>{{ takeaway }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
                
                {% if future_improvements %}
                <div class="future-improvements">
                    <h4>改进建议：</h4>
                    <ul class="improvements-list">
                        {% for improvement in future_improvements %}
                        <li>{{ improvement }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
            </div>
            
            <!-- Call to Action -->
            <div class="cta-section">
                <div class="cta-content">
                    <h4>参与贡献</h4>
                    <p>如果您对这个项目感兴趣，欢迎参与贡献！</p>
                    <div class="cta-buttons">
                        <a href="{{ github_url }}" class="cta-btn primary" target="_blank" rel="noopener noreferrer">
                            <i class="fab fa-github"></i>
                            访问项目
                        </a>
                        <a href="{{ github_url }}/fork" class="cta-btn" target="_blank" rel="noopener noreferrer">
                            <i class="fas fa-code-branch"></i>
                            Fork 项目
                        </a>
                        <a href="{{ github_url }}/issues" class="cta-btn" target="_blank" rel="noopener noreferrer">
                            <i class="fas fa-bug"></i>
                            报告问题
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    </div> <!-- 结束 article-container -->
</article>

<!-- Related Projects -->
{% if related_projects %}
<section class="related-projects">
    <div class="container">
        <h3>相关项目</h3>
        <div class="projects-grid">
            {% for project in related_projects %}
            <div class="related-project">
                <div class="project-header">
                    <h4>{{ project.name }}</h4>
                    <div class="project-stats">
                        <span class="stat">
                            <i class="fas fa-star"></i>
                            {{ project.stars }}
                        </span>
                        <span class="stat">
                            <i class="fas fa-code-branch"></i>
                            {{ project.forks }}
                        </span>
                    </div>
                </div>
                <p class="project-description">{{ project.description }}</p>
                <div class="project-footer">
                    <span class="project-language">{{ project.language }}</span>
                    <a href="{{ project.url }}" class="project-link" target="_blank" rel="noopener noreferrer">
                        查看项目 <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // 博客页面特定功能
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化滚动导航
        initScrollNavigation();
        
        // 初始化代码复制功能
        initCodeCopy();
        
        // 初始化图片懒加载
        initLazyLoading();
        
        // 初始化阅读进度
        initReadingProgress();
    });
    
    // 滚动导航功能
    function initScrollNavigation() {
        const navLinks = document.querySelectorAll('.nav-toc a');
        const sections = document.querySelectorAll('.section');
        
        function updateActiveNav() {
            let currentSection = '';
            
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top <= 100 && rect.bottom >= 100) {
                    currentSection = section.id;
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + currentSection) {
                    link.classList.add('active');
                }
            });
        }
        
        window.addEventListener('scroll', updateActiveNav);
        updateActiveNav();
    }
    
    // 代码复制功能
    function copyCode(button) {
        const codeBlock = button.closest('.code-block').querySelector('code');
        const text = codeBlock.textContent;
        
        navigator.clipboard.writeText(text).then(() => {
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.style.color = '#10b981';
            
            setTimeout(() => {
                button.innerHTML = originalContent;
                button.style.color = '';
            }, 2000);
        });
    }
    
    // 图片懒加载
    function initLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
    
    // 阅读进度
    function initReadingProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'reading-progress';
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: {{ project_color | default('#667eea') }};
            z-index: 1000;
            transition: width 0.1s ease;
        `;
        document.body.appendChild(progressBar);
        
        function updateProgress() {
            const scrollTop = window.pageYOffset;
            const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
            const progress = (scrollTop / scrollHeight) * 100;
            progressBar.style.width = progress + '%';
        }
        
        window.addEventListener('scroll', updateProgress);
    }
    
    // 分享文章
    function shareArticle() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                text: '查看这篇关于 {{ project_name }} 的技术博客',
                url: window.location.href
            });
        } else {
            // 复制链接
            navigator.clipboard.writeText(window.location.href).then(() => {
                showToast('链接已复制到剪贴板！');
            });
        }
    }
</script>
{% endblock %}