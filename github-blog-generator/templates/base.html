<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{{ description | default('GitHub项目技术博客 - 深入分析项目架构与技术实现') }}">
    <meta name="keywords" content="{{ keywords | default('GitHub, 技术博客, 项目分析, 开源项目') }}">
    <meta name="author" content="{{ author | default('GitHub博客生成器') }}">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{{ title | default('GitHub项目技术博客') }}">
    <meta property="og:description" content="{{ description | default('深入分析GitHub项目的技术实现与架构设计') }}">
    <meta property="og:type" content="article">
    <meta property="og:url" content="{{ url | default('') }}">
    <meta property="og:image" content="{{ image | default('/static/images/default-og.png') }}">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ title | default('GitHub项目技术博客') }}">
    <meta name="twitter:description" content="{{ description | default('深入分析GitHub项目的技术实现与架构设计') }}">
    <meta name="twitter:image" content="{{ image | default('/static/images/default-twitter.png') }}">
    
    <title>{{ title | default('GitHub项目技术博客') }}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/static/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/static/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/images/favicon-16x16.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <!-- Highlight.js for code syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css">
    
    <!-- Main CSS -->
    <link rel="stylesheet" href="/static/css/blog.css">
    
    <!-- Custom CSS -->
    {% block extra_css %}{% endblock %}
    
    <!-- Analytics (if enabled) -->
    {% if analytics_id %}
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ analytics_id }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ analytics_id }}');
    </script>
    {% endif %}
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "headline": "{{ title | default('GitHub项目技术博客') }}",
        "description": "{{ description | default('深入分析GitHub项目的技术实现与架构设计') }}",
        "author": {
            "@type": "Person",
            "name": "{{ author | default('GitHub博客生成器') }}"
        },
        "datePublished": "{{ date_published | default(current_date) }}",
        "dateModified": "{{ date_modified | default(current_date) }}",
        "image": "{{ image | default('/static/images/default-og.png') }}",
        "url": "{{ url | default('') }}",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "{{ url | default('') }}"
        },
        "publisher": {
            "@type": "Organization",
            "name": "GitHub博客生成器",
            "logo": {
                "@type": "ImageObject",
                "url": "/static/images/logo.png"
            }
        }
    }
    </script>
</head>
<body class="{{ theme_class | default('') }}">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-to-main">跳转到主要内容</a>
    
    <!-- Loading indicator -->
    <div id="page-loading" class="page-loading">
        <div class="loading-spinner"></div>
        <p>正在加载博客内容...</p>
    </div>
    
    <!-- Header -->
    <header class="site-header" role="banner">
        <nav class="nav-container" role="navigation" aria-label="主导航">
            <div class="nav-brand">
                <a href="/" class="brand-link">
                    <i class="fab fa-github"></i>
                    <span>GitHub博客生成器</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <button class="nav-toggle" aria-label="切换导航菜单" aria-expanded="false">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                
                <div class="nav-links">
                    <a href="/" class="nav-link">首页</a>
                    <a href="/blogs" class="nav-link">所有博客</a>
                    <a href="/about" class="nav-link">关于</a>
                    <a href="/contact" class="nav-link">联系</a>
                    
                    <!-- 主题切换 -->
                    <button class="theme-toggle" aria-label="切换主题" title="切换深色/浅色主题">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <!-- 语言切换 -->
                    <div class="language-selector">
                        <button class="lang-toggle" aria-label="选择语言">
                            <i class="fas fa-globe"></i>
                            <span>中文</span>
                        </button>
                        <div class="lang-dropdown">
                            <a href="?lang=zh" class="lang-option">中文</a>
                            <a href="?lang=en" class="lang-option">English</a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <!-- Breadcrumb Navigation -->
    {% if breadcrumbs %}
    <nav class="breadcrumb-nav" aria-label="面包屑导航">
        <div class="container">
            <ol class="breadcrumb">
                {% for crumb in breadcrumbs %}
                <li class="breadcrumb-item{% if loop.last %} active{% endif %}">
                    {% if not loop.last %}
                    <a href="{{ crumb.url }}">{{ crumb.title }}</a>
                    {% else %}
                    <span>{{ crumb.title }}</span>
                    {% endif %}
                </li>
                {% endfor %}
            </ol>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            {% block content %}
            <!-- 页面内容将在这里渲染 -->
            {% endblock %}
        </div>
    </main>
    
    <!-- Sidebar (if enabled) -->
    {% if show_sidebar %}
    <aside class="sidebar" role="complementary" aria-label="侧边栏">
        <div class="sidebar-content">
            {% block sidebar %}
            <!-- 侧边栏内容 -->
            <div class="sidebar-section">
                <h3>相关链接</h3>
                <ul class="sidebar-links">
                    <li><a href="{{ github_url }}" target="_blank" rel="noopener noreferrer">
                        <i class="fab fa-github"></i> 项目源码
                    </a></li>
                    <li><a href="{{ github_url }}/issues" target="_blank" rel="noopener noreferrer">
                        <i class="fas fa-bug"></i> 问题反馈
                    </a></li>
                    <li><a href="{{ github_url }}/wiki" target="_blank" rel="noopener noreferrer">
                        <i class="fas fa-book"></i> 项目文档
                    </a></li>
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>分享这篇博客</h3>
                <div class="share-buttons">
                    <button class="share-btn twitter" data-platform="twitter">
                        <i class="fab fa-twitter"></i>
                    </button>
                    <button class="share-btn facebook" data-platform="facebook">
                        <i class="fab fa-facebook"></i>
                    </button>
                    <button class="share-btn linkedin" data-platform="linkedin">
                        <i class="fab fa-linkedin"></i>
                    </button>
                    <button class="share-btn copy" data-platform="copy">
                        <i class="fas fa-link"></i>
                    </button>
                </div>
            </div>
            {% endblock %}
        </div>
    </aside>
    {% endif %}
    
    <!-- Back to Top Button -->
    <button class="back-to-top" aria-label="返回顶部" title="返回页面顶部">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <!-- Footer -->
    <footer class="site-footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>GitHub博客生成器</h4>
                    <p>让技术分享更简单，将GitHub项目转换为精美的技术博客。</p>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="GitHub" target="_blank">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="Twitter" target="_blank">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" aria-label="LinkedIn" target="_blank">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul class="footer-links">
                        <li><a href="/">首页</a></li>
                        <li><a href="/blogs">所有博客</a></li>
                        <li><a href="/about">关于我们</a></li>
                        <li><a href="/contact">联系我们</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>帮助与支持</h4>
                    <ul class="footer-links">
                        <li><a href="/help">使用指南</a></li>
                        <li><a href="/faq">常见问题</a></li>
                        <li><a href="/privacy">隐私政策</a></li>
                        <li><a href="/terms">服务条款</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>技术支持</h4>
                    <p>如有技术问题，请联系我们：</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +86 ************</p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-copyright">
                    <p>&copy; {{ current_year | default('2024') }} GitHub博客生成器. 保留所有权利.</p>
                </div>
                <div class="footer-credits">
                    <p>由 <a href="#" target="_blank">开源社区</a> 提供技术支持</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Cookie Notice (if enabled) -->
    {% if show_cookie_notice %}
    <div id="cookie-notice" class="cookie-notice">
        <div class="cookie-content">
            <p>我们使用Cookie来改善您的浏览体验。继续使用本网站即表示您同意我们的Cookie政策。</p>
            <div class="cookie-actions">
                <button class="cookie-btn accept">接受</button>
                <button class="cookie-btn decline">拒绝</button>
                <a href="/privacy" class="cookie-link">了解更多</a>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- JavaScript -->
    <!-- Highlight.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/java.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/html.min.js"></script>
    
    <!-- Main JavaScript -->
    <script src="/static/js/blog.js"></script>
    
    <!-- Base Template JavaScript -->
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 隐藏加载指示器
            const loading = document.getElementById('page-loading');
            if (loading) {
                loading.style.display = 'none';
            }
            
            // 初始化代码高亮
            hljs.highlightAll();
            
            // 初始化导航菜单
            initNavigation();
            
            // 初始化主题切换
            initThemeToggle();
            
            // 初始化Cookie通知
            initCookieNotice();
            
            // 初始化返回顶部按钮
            initBackToTop();
            
            // 初始化分享功能
            initShareButtons();
        });
        
        // 导航菜单功能
        function initNavigation() {
            const navToggle = document.querySelector('.nav-toggle');
            const navLinks = document.querySelector('.nav-links');
            
            if (navToggle && navLinks) {
                navToggle.addEventListener('click', function() {
                    const expanded = navToggle.getAttribute('aria-expanded') === 'true';
                    navToggle.setAttribute('aria-expanded', !expanded);
                    navLinks.classList.toggle('active');
                });
            }
        }
        
        // 主题切换功能
        function initThemeToggle() {
            const themeToggle = document.querySelector('.theme-toggle');
            
            if (themeToggle) {
                // 检查本地存储的主题
                const savedTheme = localStorage.getItem('theme') || 'light';
                document.body.classList.toggle('dark-theme', savedTheme === 'dark');
                updateThemeIcon(savedTheme === 'dark');
                
                themeToggle.addEventListener('click', function() {
                    const isDark = document.body.classList.toggle('dark-theme');
                    localStorage.setItem('theme', isDark ? 'dark' : 'light');
                    updateThemeIcon(isDark);
                });
            }
        }
        
        function updateThemeIcon(isDark) {
            const icon = document.querySelector('.theme-toggle i');
            if (icon) {
                icon.className = isDark ? 'fas fa-sun' : 'fas fa-moon';
            }
        }
        
        // Cookie通知功能
        function initCookieNotice() {
            const cookieNotice = document.getElementById('cookie-notice');
            if (!cookieNotice) return;
            
            // 检查是否已同意
            if (localStorage.getItem('cookieConsent')) {
                cookieNotice.style.display = 'none';
                return;
            }
            
            const acceptBtn = cookieNotice.querySelector('.accept');
            const declineBtn = cookieNotice.querySelector('.decline');
            
            acceptBtn?.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'accepted');
                cookieNotice.style.display = 'none';
            });
            
            declineBtn?.addEventListener('click', function() {
                localStorage.setItem('cookieConsent', 'declined');
                cookieNotice.style.display = 'none';
            });
        }
        
        // 返回顶部功能
        function initBackToTop() {
            const backToTop = document.querySelector('.back-to-top');
            
            if (backToTop) {
                window.addEventListener('scroll', function() {
                    backToTop.classList.toggle('visible', window.pageYOffset > 300);
                });
                
                backToTop.addEventListener('click', function() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }
        }
        
        // 分享功能
        function initShareButtons() {
            document.querySelectorAll('.share-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const platform = this.dataset.platform;
                    const url = window.location.href;
                    const title = document.title;
                    
                    handleShare(platform, url, title);
                });
            });
        }
        
        function handleShare(platform, url, title) {
            const shareUrls = {
                twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
                facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
                linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`
            };
            
            if (platform === 'copy') {
                navigator.clipboard.writeText(url).then(() => {
                    showToast('链接已复制到剪贴板！');
                });
            } else if (shareUrls[platform]) {
                window.open(shareUrls[platform], '_blank', 'width=600,height=400');
            }
        }
        
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'toast-message';
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                bottom: 2rem;
                left: 50%;
                transform: translateX(-50%);
                background: #10b981;
                color: white;
                padding: 1rem 2rem;
                border-radius: 2rem;
                z-index: 1000;
                animation: slideUp 0.3s ease;
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
    
    <!-- Custom JavaScript -->
    {% block extra_js %}{% endblock %}
    
    <!-- Service Worker Registration (if enabled) -->
    {% if enable_pwa %}
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(error) {
                        console.log('ServiceWorker registration failed');
                    });
            });
        }
    </script>
    {% endif %}
</body>
</html>