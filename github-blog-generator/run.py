#!/usr/bin/env python3
"""
GitHub博客生成器 - 启动脚本
一键启动前后端服务
"""

import os
import sys
import subprocess
import threading
import webbrowser
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                  GitHub 博客生成器                           ║
║               GitHub Blog Generator                          ║
║                                                              ║
║  🚀 将您的 GitHub 项目转换为精美的技术博客                    ║
║  📱 现代化响应式设计 | 🎨 智能内容生成                       ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_requirements():
    """检查必需的依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要 Python 3.7 或更高版本")
        return False
    
    # 检查必需文件
    required_files = [
        "backend/app.py",
        "backend/github_api.py", 
        "backend/content_generator.py",
        "frontend/index.html",
        "requirements.txt"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ 缺少必需文件: {file_path}")
            return False
    
    print("✅ 系统依赖检查通过")
    return True

def install_dependencies():
    """安装Python依赖"""
    print("📦 安装Python依赖...")
    
    try:
        # 添加trusted-host参数解决SSL证书问题
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "--trusted-host", "pypi.org",
            "--trusted-host", "pypi.python.org", 
            "--trusted-host", "files.pythonhosted.org",
            "-r", "requirements.txt"
        ], check=True, capture_output=True, text=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        print("💡 尝试手动安装: pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt")
        return False

def start_backend():
    """启动后端服务"""
    print("🔧 启动后端服务...")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['FLASK_APP'] = 'backend/app.py'
        env['FLASK_ENV'] = 'development'
        env['FLASK_DEBUG'] = '1'
        
        # 启动Flask服务器
        process = subprocess.Popen([
            sys.executable, "-m", "flask", "run", 
            "--host", "0.0.0.0", 
            "--port", "5000"
        ], env=env, cwd=os.getcwd())
        
        print("✅ 后端服务启动成功 (http://localhost:5000)")
        return process
        
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🌐 启动前端服务...")
    
    try:
        # 检查是否有http.server模块
        import http.server
        import socketserver
        
        # 创建HTTP服务器
        PORT = 8080
        Handler = http.server.SimpleHTTPRequestHandler
        
        class CustomHandler(Handler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, directory="frontend", **kwargs)
        
        with socketserver.TCPServer(("", PORT), CustomHandler) as httpd:
            print(f"✅ 前端服务启动成功 (http://localhost:{PORT})")
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(2)  # 等待服务器启动
                webbrowser.open(f'http://localhost:{PORT}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            httpd.serve_forever()
            
    except Exception as e:
        print(f"❌ 前端服务启动失败: {e}")
        return None

def start_development_server():
    """启动开发服务器（推荐方式）"""
    print("🚀 启动开发服务器...")
    
    try:
        # 启动后端
        backend_process = start_backend()
        if not backend_process:
            return False
        
        # 等待后端启动
        time.sleep(3)
        
        # 在新线程中启动前端
        def run_frontend():
            try:
                os.chdir("frontend")
                subprocess.run([
                    sys.executable, "-m", "http.server", "8080"
                ], check=True)
            except:
                print("前端服务器已停止")
            finally:
                os.chdir("..")
        
        frontend_thread = threading.Thread(target=run_frontend, daemon=True)
        frontend_thread.start()
        
        # 等待前端启动
        time.sleep(2)
        
        print("\n" + "="*60)
        print("🎉 服务启动完成!")
        print("📍 前端地址: http://localhost:8080")
        print("📍 后端API: http://localhost:5000")
        print("📖 使用指南: docs/README.md")
        print("="*60)
        print("\n💡 提示:")
        print("- 在浏览器中访问 http://localhost:8080 开始使用")
        print("- 按 Ctrl+C 停止服务")
        print("- 如需帮助，请查看 docs/README.md\n")
        
        # 自动打开浏览器
        webbrowser.open('http://localhost:8080')
        
        # 等待用户停止
        try:
            backend_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            backend_process.terminate()
            print("✅ 服务已停止")
            
        return True
        
    except Exception as e:
        print(f"❌ 开发服务器启动失败: {e}")
        return False

def show_help():
    """显示帮助信息"""
    help_text = """
使用方法:
  python run.py [选项]

选项:
  --help, -h     显示此帮助信息
  --dev          启动开发服务器 (默认)
  --frontend     仅启动前端服务
  --backend      仅启动后端服务
  --install      仅安装依赖
  --check        仅检查系统依赖

示例:
  python run.py                    # 启动完整服务
  python run.py --frontend         # 仅启动前端
  python run.py --install          # 安装依赖
"""
    print(help_text)

def main():
    """主函数"""
    # 解析命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h']:
            show_help()
            return
        elif arg == '--check':
            print_banner()
            check_requirements()
            return
        elif arg == '--install':
            print_banner()
            install_dependencies()
            return
        elif arg == '--frontend':
            print_banner()
            start_frontend()
            return
        elif arg == '--backend':
            print_banner()
            backend_process = start_backend()
            if backend_process:
                try:
                    backend_process.wait()
                except KeyboardInterrupt:
                    backend_process.terminate()
            return
    
    # 默认启动完整服务
    print_banner()
    
    # 检查依赖
    if not check_requirements():
        print("\n💡 请先解决上述问题，然后重新运行")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("\n💡 依赖安装失败，请手动运行: pip install -r requirements.txt")
        return
    
    # 启动服务
    start_development_server()

if __name__ == "__main__":
    main()