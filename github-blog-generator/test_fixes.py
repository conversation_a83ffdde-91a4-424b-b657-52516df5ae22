#!/usr/bin/env python3
"""
测试脚本 - 验证所有修复是否正常工作
"""

import requests
import json
import time
import sys

def test_backend_connection():
    """测试后端连接"""
    print("🔧 测试后端连接...")
    try:
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code == 200:
            print("✅ 后端连接正常")
            return True
        else:
            print(f"❌ 后端连接失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_blog_generation():
    """测试博客生成功能"""
    print("📝 测试博客生成功能...")
    try:
        test_data = {
            "github_url": "https://github.com/microsoft/vscode",
            "custom_title": "VS Code 项目分析"
        }
        
        response = requests.post(
            'http://localhost:5000/api/generate-blog',
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                blog_url = result.get('blog_url')
                print(f"✅ 博客生成成功: {blog_url}")
                
                # 测试博客页面访问
                blog_response = requests.get(f'http://localhost:5000{blog_url}', timeout=10)
                if blog_response.status_code == 200:
                    print("✅ 博客页面访问正常")
                    return True
                else:
                    print(f"❌ 博客页面访问失败: HTTP {blog_response.status_code}")
                    return False
            else:
                print(f"❌ 博客生成失败: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ 博客生成请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 博客生成测试失败: {e}")
        return False

def test_frontend_access():
    """测试前端页面访问"""
    print("🌐 测试前端页面访问...")
    try:
        # 测试主页
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code == 200 and 'GitHub 项目可视化博客生成器' in response.text:
            print("✅ 前端主页访问正常")
            
            # 测试静态文件
            css_response = requests.get('http://localhost:5000/frontend/style.css', timeout=5)
            js_response = requests.get('http://localhost:5000/frontend/script.js', timeout=5)
            
            if css_response.status_code == 200 and js_response.status_code == 200:
                print("✅ 前端静态文件加载正常")
                return True
            else:
                print("❌ 前端静态文件加载失败")
                return False
        else:
            print(f"❌ 前端页面访问失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端页面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试GitHub博客生成器...")
    print("="*50)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    tests = [
        ("后端连接", test_backend_connection),
        ("前端访问", test_frontend_access),
        ("博客生成", test_blog_generation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        results[test_name] = test_func()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("="*50)
    
    if all_passed:
        print("🎉 所有测试通过！项目修复成功！")
        print("\n💡 使用说明:")
        print("1. 访问 http://localhost:5000 使用博客生成器")
        print("2. 输入 GitHub 项目 URL")
        print("3. 点击生成博客，自动跳转到生成的博客页面")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())