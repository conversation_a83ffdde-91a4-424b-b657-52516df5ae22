#!/usr/bin/env python3
"""
在Python IDLE或其他Python环境中运行
复制此代码到Python环境中执行
"""

import os
import sys
import webbrowser
from threading import Timer

def start_server():
    """启动服务器"""
    print("🚀 启动GitHub博客生成器...")
    print("=" * 50)
    
    # 获取当前文件所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(current_dir)
    
    # 添加backend到Python路径
    backend_path = os.path.join(current_dir, 'backend')
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)
    
    try:
        # 导入Flask应用
        from backend.app import app
        
        print("✅ Flask应用加载成功")
        print("📍 服务地址: http://localhost:5000")
        print("💡 服务启动后会自动打开浏览器")
        print("💡 在Python环境中按停止按钮或Ctrl+C停止服务")
        print("=" * 50)
        
        # 延迟打开浏览器
        def open_browser():
            import time
            time.sleep(3)
            webbrowser.open('http://localhost:5000')
            print("🌐 浏览器已打开，请访问 http://localhost:5000")
        
        Timer(1, open_browser).start()
        
        # 启动Flask服务器
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所需依赖:")
        print("   pip install flask requests matplotlib seaborn pandas")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    start_server()

# 如果在IDLE中运行，可以直接调用：
# start_server()
