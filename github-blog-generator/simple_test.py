#!/usr/bin/env python3
"""
简单测试脚本
"""

import requests
import json
import time

def test_simple():
    """简单测试"""
    try:
        print("🔍 测试服务器连接...")
        response = requests.get('http://localhost:5000/', timeout=5)
        print(f"主页状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 服务器正常运行")
            
            print("📤 测试API调用...")
            api_data = {
                'github_url': 'https://github.com/torvalds/linux',
                'custom_title': '测试博客'
            }
            
            api_response = requests.post(
                'http://localhost:5000/api/generate-blog',
                json=api_data,
                timeout=30
            )
            
            print(f"API状态码: {api_response.status_code}")
            print(f"API响应: {api_response.text[:200]}...")
            
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_simple()
