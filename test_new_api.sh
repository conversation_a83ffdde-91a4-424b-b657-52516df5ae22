#!/bin/bash

echo "🧪 测试新的Claude API密钥"
echo "========================"
echo ""

# 设置新的API配置
export ANTHROPIC_AUTH_TOKEN="sk-v7MSBdI0VJVKaL6boyKJCeskKBkTSA1Gcm9Pn6s2kWWyjyYi"
export ANTHROPIC_API_KEY="sk-v7MSBdI0VJVKaL6boyKJCeskKBkTSA1Gcm9Pn6s2kWWyjyYi"
export ANTHROPIC_BASE_URL="https://anyrouter.top"

echo "📋 新的API配置："
echo "  API Key: ${ANTHROPIC_AUTH_TOKEN:0:20}..."
echo "  Base URL: $ANTHROPIC_BASE_URL"
echo ""

echo "🔍 测试步骤："
echo ""

# 测试1: 基本API调用
echo "1️⃣ 测试基本API调用："
echo "   发送测试请求..."

API_RESPONSE=$(curl -s -w "\nSTATUS:%{http_code}" \
  -X POST "$ANTHROPIC_BASE_URL/v1/messages" \
  -H "Content-Type: application/json" \
  -H "x-api-key: $ANTHROPIC_AUTH_TOKEN" \
  -H "anthropic-version: 2023-06-01" \
  -d '{
    "model": "claude-3-sonnet-20240229",
    "max_tokens": 20,
    "messages": [{"role": "user", "content": "Hello, please respond with just: API test successful"}]
  }' 2>/dev/null)

HTTP_STATUS=$(echo "$API_RESPONSE" | grep "STATUS:" | cut -d: -f2)
RESPONSE_BODY=$(echo "$API_RESPONSE" | grep -v "STATUS:")

echo "   📊 HTTP状态码: $HTTP_STATUS"

case $HTTP_STATUS in
    200)
        echo "   ✅ API调用成功！"
        echo "   📝 响应内容: $(echo "$RESPONSE_BODY" | head -c 200)..."
        ;;
    520)
        echo "   ❌ 520错误 - 服务器错误"
        ;;
    502)
        echo "   ❌ 502错误 - 网关错误"
        ;;
    401)
        echo "   ❌ 401错误 - API密钥无效"
        ;;
    429)
        echo "   ❌ 429错误 - 请求过于频繁"
        ;;
    *)
        echo "   ⚠️  其他错误 (状态码: $HTTP_STATUS)"
        if [ -n "$RESPONSE_BODY" ]; then
            echo "   📝 错误信息: $RESPONSE_BODY"
        fi
        ;;
esac

echo ""

# 测试2: Claude CLI
echo "2️⃣ 测试Claude CLI："
echo "   运行Claude CLI命令..."

# 使用--print参数进行非交互式测试
CLAUDE_OUTPUT=$(echo "Hello, please respond with: Claude CLI test successful" | claude --print 2>&1)
CLAUDE_EXIT_CODE=$?

echo "   📊 Claude CLI退出码: $CLAUDE_EXIT_CODE"

if [ $CLAUDE_EXIT_CODE -eq 0 ]; then
    echo "   ✅ Claude CLI工作正常！"
    echo "   📝 Claude响应: $(echo "$CLAUDE_OUTPUT" | head -c 200)..."
else
    echo "   ❌ Claude CLI出现错误"
    echo "   📝 错误信息: $(echo "$CLAUDE_OUTPUT" | head -c 200)..."
fi

echo ""

# 测试3: 交互式Claude CLI
echo "3️⃣ 准备交互式测试："
echo "   如果上述测试成功，您可以运行以下命令进行交互式测试："
echo "   cd /Users/<USER>/gemini cli"
echo "   claude"
echo ""

echo "🏁 测试完成"
echo ""

# 总结
if [ "$HTTP_STATUS" = "200" ] && [ $CLAUDE_EXIT_CODE -eq 0 ]; then
    echo "🎉 恭喜！新的API密钥配置成功！"
    echo "   ✅ API调用正常"
    echo "   ✅ Claude CLI正常"
    echo ""
    echo "📝 配置已保存到："
    echo "   - ~/.zshrc"
    echo "   - ~/.bashrc"
    echo "   - ~/.bash_profile"
    echo ""
    echo "🚀 现在您可以直接使用 'claude' 命令了！"
elif [ "$HTTP_STATUS" = "200" ]; then
    echo "⚠️  API调用成功，但Claude CLI有问题"
    echo "   建议检查Claude CLI安装或重新安装"
elif [ $CLAUDE_EXIT_CODE -eq 0 ]; then
    echo "⚠️  Claude CLI正常，但API调用有问题"
    echo "   建议检查网络连接或API密钥"
else
    echo "❌ 仍有问题需要解决"
    echo "   建议检查API密钥有效性和网络连接"
fi
